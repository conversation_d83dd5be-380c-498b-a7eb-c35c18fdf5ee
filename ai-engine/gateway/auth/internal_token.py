import datetime
import typing

import structlog
from jose import jwt

log = structlog.get_logger("hakimo.gateway", module="Internal Token Auth")


def get_shared_alarm_token(
    alarm_id: str,
    duration_hours: int,
    secret: str,
    partitionKey: typing.Optional[int] = None,
) -> str:
    now = datetime.datetime.now()
    claims = {
        "exp": (now + datetime.timedelta(hours=duration_hours)).timestamp(),
        "iat": now.timestamp(),
        "nbf": now.timestamp(),
        "alarm_id": alarm_id,
    }
    if partitionKey is not None:
        claims["partitionKey"] = partitionKey
    return jwt.encode(claims, secret, algorithm="HS256")


def get_shared_location_alarm_token(
    location_alarm_id: typing.Optional[int],
    duration_hours: int,
    secret: str,
    partitionKey: typing.Optional[int] = None,
) -> str:
    now = datetime.datetime.now()
    claims = {
        "exp": (now + datetime.timedelta(hours=duration_hours)).timestamp(),
        "iat": now.timestamp(),
        "nbf": now.timestamp(),
        "location_alarm_id": location_alarm_id,
    }
    if partition<PERSON>ey is not None:
        claims["partitionKey"] = partitionKey
    return jwt.encode(claims, secret, algorithm="HS256")


def get_shared_livestream_data_token(
    camera_id: typing.Optional[str],
    tenant_id: typing.Optional[str],
    user_uuid: typing.Optional[str],
    livestream_url: typing.Optional[str],
    duration_hours: int,
    secret: str,
) -> str:
    now = datetime.datetime.now()
    claims = {
        "exp": (now + datetime.timedelta(hours=duration_hours)).timestamp(),
        "iat": now.timestamp(),
        "nbf": now.timestamp(),
        "camera_id": camera_id,
        "tenant_id": tenant_id,
        "user_uuid": user_uuid,
        "livestream_url": livestream_url,
    }
    return jwt.encode(claims, secret, algorithm="HS256")


def get_shared_alarm_group_token(
    alarm_group_id: str,
    duration_hours: int,
    secret: str,
) -> str:
    now = datetime.datetime.now()
    claims = {
        "exp": (now + datetime.timedelta(hours=duration_hours)).timestamp(),
        "iat": now.timestamp(),
        "nbf": now.timestamp(),
        "alarm_group_id": alarm_group_id,
    }
    return jwt.encode(claims, secret, algorithm="HS256")


def get_login_token(user_email: str, duration_hours: int, secret: str) -> str:
    now = datetime.datetime.now()
    claims = {
        "exp": (now + datetime.timedelta(hours=duration_hours)).timestamp(),
        "iat": now.timestamp(),
        "nbf": now.timestamp(),
        "http://hakimo.ai/email": user_email,
    }
    return jwt.encode(claims, secret, algorithm="HS256")


def decode_token(token: str, secret: str) -> typing.Dict[str, str]:
    return jwt.decode(token, secret, algorithms=["HS256"])

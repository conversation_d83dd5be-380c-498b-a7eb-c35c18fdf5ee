import typing

import flask

import controller as ctrl
from gateway.http_api.orm.message_acknowledgements.blueprint import (
    acknowledgements_blueprint,
)
from gateway.http_api.orm.vision_hosts.blueprint import vision_hosts_blueprint

from .ai_outputs_location_alarm import ai_outputs_location_alarms_blueprint
from .alarm import alarm_blueprint_v2
from .alarm_type import alarm_type_blueprint_v2
from .alarm_type_notifications import alarm_type_notif_blueprint_v2
from .audio_devices import audio_device_api
from .auth import auth_blueprint_v2
from .camera import camera_blueprint_v2
from .door import door_blueprint_v2
from .door_camera_params import dcp_blueprint
from .door_groups import door_groups_blueprint
from .employee import employee_blueprint_v2
from .escalation import escalations_blueprint
from .location_alarm import location_alarms_blueprint
from .location_incidents import location_incidents_blueprint
from .locations import locations_blueprint
from .person_profile import person_profile_api
from .shared_endpoints import (
    shared_alarm_blueprint_guarded,
    shared_alarm_blueprint_token_guarded,
    shared_alarm_group_blueprint_guarded,
    shared_alarm_group_blueprint_token_guarded,
    shared_live_view_blueprint_guarded,
    shared_live_view_blueprint_token_guarded,
    shared_location_alarm_blueprint_guarded,
    shared_location_alarm_blueprint_token_guarded,
)
from .sms import sms_blueprint
from .sop import sop_api
from .source_entity import source_entity_blueprint_v2
from .tenant import tenant_blueprint_v2
from .timezones import timezones_blueprint_v2
from .user import user_blueprint_v2


def get_orm_blueprints(
    ctrl_map: ctrl.ControllerMap,
) -> typing.Sequence[typing.Tuple[flask.Blueprint, str]]:
    ret = []
    # v2 URLs that perform token based auth
    ret.append((sop_api(ctrl_map), "/v2/orm/sop"))
    ret.append((dcp_blueprint(ctrl_map), "/v2/orm/dcp"))
    ret.append((alarm_blueprint_v2(ctrl_map), "/v2/orm/alarm"))
    ret.append((user_blueprint_v2(ctrl_map), "/v2/orm/user"))
    ret.append((tenant_blueprint_v2(ctrl_map), "/v2/orm/tenant"))
    ret.append((employee_blueprint_v2(ctrl_map), "/v2/orm/employee"))
    ret.append((door_blueprint_v2(ctrl_map), "/v2/orm/door"))
    ret.append((source_entity_blueprint_v2(ctrl_map), "/v2/orm/source_entity"))
    ret.append((camera_blueprint_v2(ctrl_map), "/v2/orm/camera"))
    ret.append((alarm_type_blueprint_v2(ctrl_map), "/v2/orm/alarm_type"))
    ret.append(
        (
            alarm_type_notif_blueprint_v2(ctrl_map),
            "/v2/orm/alarm_type/notifications",
        )
    )
    ret.append(
        (shared_alarm_blueprint_guarded(ctrl_map), "/v2/orm/shared/alarm")
    )
    ret.append(
        (
            shared_alarm_group_blueprint_guarded(ctrl_map),
            "/v2/orm/shared/alarm_group",
        )
    )
    ret.append((door_groups_blueprint(ctrl_map), "/v2/orm/door_groups"))
    ret.append(
        (location_alarms_blueprint(ctrl_map), "/v2/orm/location_alarms")
    )
    ret.append((audio_device_api(ctrl_map), "/v2/orm/audio_devices"))
    ret.append(
        (
            shared_location_alarm_blueprint_guarded(ctrl_map),
            "/v2/orm/shared/location_alarm",
        )
    )
    ret.append(
        (
            shared_live_view_blueprint_guarded(ctrl_map),
            "/v2/orm/shared/live_view",
        )
    )
    ret.append((locations_blueprint(ctrl_map), "/v2/orm/locations"))
    ret.append((timezones_blueprint_v2(ctrl_map), "/v2/orm/timezones"))
    ret.append((sms_blueprint(ctrl_map), "/v2/orm/sms"))
    ret.append(
        (location_incidents_blueprint(ctrl_map), "/v2/orm/location_incidents")
    )
    ret.append((escalations_blueprint(ctrl_map), "/v2/orm/escalations"))
    ret.append((vision_hosts_blueprint(ctrl_map), "/v2/orm/vision_hosts"))
    ret.append(
        (acknowledgements_blueprint(ctrl_map), "/v2/orm/acknowledgements")
    )
    ret.append(
        (
            ai_outputs_location_alarms_blueprint(ctrl_map),
            "/v2/orm/ai_outputs_location_alarm",
        )
    )
    ret.append((person_profile_api(ctrl_map), "/v2/orm/person_profile"))

    # unguarded endpoint
    ret.append((auth_blueprint_v2(ctrl_map), "/auth"))

    # shared URLs that have custom internal token auth
    ret.append(
        (shared_alarm_blueprint_token_guarded(ctrl_map), "/shared/v2/alarm")
    )
    ret.append(
        (
            shared_alarm_group_blueprint_token_guarded(ctrl_map),
            "/shared/v2/alarm_group",
        )
    )
    ret.append(
        (
            shared_location_alarm_blueprint_token_guarded(ctrl_map),
            "/shared/v2/location_alarm",
        )
    )
    ret.append(
        (
            shared_live_view_blueprint_token_guarded(ctrl_map),
            "/shared/v2/camera",
        )
    )

    return ret

import structlog
from flask import Blueprint, request

import controller as ctrl

from .endpoint import SharedAlarmGroupEndpoint

log = structlog.get_logger(
    "hakimo.orm.shared.alarm_group",
    module="Shared Alarm Group Blueprint",
)


def shared_alarm_group_blueprint_guarded(cm: ctrl.ControllerMap):
    api = Blueprint("shared_alarm_groups_api_v2", __name__)
    endpoint = SharedAlarmGroupEndpoint(cm)

    @api.route("/token/<alarm_group_id>", methods=["GET"])
    def get_alarm_group_token_wrapper(
        alarm_group_id,
    ):  # pylint: disable=unused-variable
        query_params = request.args.to_dict()
        return endpoint.get_token(alarm_group_id, query_params=query_params)

    return api


def shared_alarm_group_blueprint_token_guarded(cm: ctrl.ControllerMap):
    api = Blueprint("shared_alarm_groups_token_guarded_api_v2", __name__)
    endpoint = SharedAlarmGroupEndpoint(cm)

    @api.route("", methods=["GET"])
    def get_shared_alarm_group_wrapper():  # pylint: disable=unused-variable
        shared_alarm_group_params = request.args.to_dict()
        return endpoint.get_shared_alarm_group(shared_alarm_group_params)

    return api

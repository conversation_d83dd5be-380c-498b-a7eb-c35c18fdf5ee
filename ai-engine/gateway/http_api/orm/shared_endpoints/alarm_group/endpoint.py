import typing
from http import HTTPStatus

import structlog

import controller as ctrl
from common_utils.io_helpers import read_file
from config import backend_config as config
from db_controller.alarm_group.alarm_group_controller import AlarmGroupController as DBAlarmGroupController
from gateway.auth.internal_token import get_shared_alarm_group_token
from gateway.common.endpoint_guard import guard_endpoint, guard_shared_endpoint
import gateway.http_api.orm.shared_endpoints.alarm.endpoint as ep
from models_rds.users import Users

log = structlog.get_logger(
    "hakimo.orm.shared.alarm_group", module="Shared alarm group blueprint"
)


class SharedAlarmGroupEndpoint:
    def __init__(self, controller: ctrl.ControllerMap):
        self._ctrl_map = controller
        self._db_alarm_group_controller = DBAlarmGroupController(
            controller.db, controller.db
        )
        self._jwt_secret = read_file(
            config.HAIE.ormAuth["jwtKey"], missing="NOT_FOUND"
        )
        if self._jwt_secret == "NOT_FOUND":
            log.error("No JWT secret found")

    @guard_endpoint(["alarm_group/details:share"])
    def get_token(
        self,
        alarm_group_id: str,
        query_params: dict,
        user: typing.Optional[Users] = None,
    ):
        assert user is not None
        api_resp: typing.Dict[str, typing.Any] = {}
        try:
            params = ep.unmarshal_alarm_token_query(query_params)
        except ValueError as exc:
            log.info("Bad params to shared alarm group token endpoint", exc_info=exc)
            api_resp["status"] = HTTPStatus.BAD_REQUEST.value
            api_resp["message"] = "Bad query to shared alarm group token endpoint"
            return (api_resp, HTTPStatus.BAD_REQUEST.value)
        
        api_resp["payload"] = {
            "accessToken": get_shared_alarm_group_token(
                alarm_group_id,
                params["duration_hours"],
                self._jwt_secret,
            )
        }
        api_resp["message"] = "Processed successfully"
        api_resp["status"] = HTTPStatus.OK.value
        return api_resp, HTTPStatus.OK.value

    @guard_shared_endpoint
    def get_shared_alarm_group(self, shared_alarm_group_params: dict, token_params: dict = None):
        api_resp: typing.Dict[str, typing.Any] = {}
        assert token_params is not None

        if "alarm_group_id" not in token_params:
            api_resp["status"] = HTTPStatus.UNAUTHORIZED.value
            api_resp["message"] = "Invalid shared token provided"
            return api_resp, HTTPStatus.UNAUTHORIZED.value

        alarm_group_id = token_params["alarm_group_id"]

        try:
            # Get alarm group from database
            alarm_group = self._db_alarm_group_controller.get_alarm_group_by_id(alarm_group_id)

            if not alarm_group:
                api_resp["status"] = HTTPStatus.NOT_FOUND.value
                api_resp["message"] = "Alarm group not found"
                return api_resp, HTTPStatus.NOT_FOUND.value

            # Convert to the expected format
            api_resp["payload"] = self._marshal_alarm_group(alarm_group)
            api_resp["message"] = "Processed successfully"
            api_resp["status"] = HTTPStatus.OK.value
            return api_resp, HTTPStatus.OK.value

        except Exception as exc:
            log.error("Error getting shared alarm group", exc_info=exc, alarm_group_id=alarm_group_id)
            api_resp["status"] = HTTPStatus.INTERNAL_SERVER_ERROR.value
            api_resp["message"] = "Internal server error"
            return api_resp, HTTPStatus.INTERNAL_SERVER_ERROR.value

    def _marshal_alarm_group(self, alarm_group):
        """Convert database alarm group to frontend format"""
        # Get location information from camera group
        location_info = self._get_location_info(alarm_group.camera_group_id)

        return {
            "id": alarm_group.id,
            "timestamp": alarm_group.start_time_utc.isoformat() if alarm_group.start_time_utc else "",
            "tenantId": alarm_group.tenant_id,
            "location": location_info,
            "alarms": [],  # TODO: Fetch and marshal actual alarm events
            "alarmCount": 0,  # TODO: Get actual alarm count
            "status": alarm_group.state.value if hasattr(alarm_group.state, 'value') else str(alarm_group.state),
            "severity": alarm_group.severity.value if hasattr(alarm_group.severity, 'value') else str(alarm_group.severity),
            "escalations": []  # TODO: Fetch escalations if needed
        }

    def _get_location_info(self, camera_group_id):
        """Get location information for the camera group"""
        try:
            # Try to get location from camera group
            # For now, return basic info - this could be enhanced to fetch actual location data
            return {
                "id": camera_group_id,
                "name": f"Location {camera_group_id}",
                "tenant_id": "default"  # This should be fetched from actual location data
            }
        except Exception as e:
            log.warning("Could not fetch location info", camera_group_id=camera_group_id, error=str(e))
            return {
                "id": camera_group_id or "unknown",
                "name": "Unknown Location",
                "tenant_id": "default"
            }

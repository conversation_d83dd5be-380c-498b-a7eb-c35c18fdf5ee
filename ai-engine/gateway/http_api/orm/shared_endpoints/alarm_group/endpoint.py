import typing
from http import HTTPStatus

import structlog

import controller as ctrl
from common_utils.io_helpers import read_file
from config import backend_config as config
from gateway.auth.internal_token import get_shared_alarm_group_token
from gateway.common.endpoint_guard import guard_endpoint, guard_shared_endpoint
import gateway.http_api.orm.shared_endpoints.alarm.endpoint as ep
from models_rds.users import Users

log = structlog.get_logger(
    "hakimo.orm.shared.alarm_group", module="Shared alarm group blueprint"
)


class SharedAlarmGroupEndpoint:
    def __init__(self, controller: ctrl.ControllerMap):
        self._ctrl_map = controller
        self._jwt_secret = read_file(
            config.HAIE.ormAuth["jwtKey"], missing="NOT_FOUND"
        )
        if self._jwt_secret == "NOT_FOUND":
            log.error("No JWT secret found")

    @guard_endpoint(["alarm_group/details:share"])
    def get_token(
        self,
        alarm_group_id: str,
        query_params: dict,
        user: typing.Optional[Users] = None,
    ):
        assert user is not None
        api_resp: typing.Dict[str, typing.Any] = {}
        try:
            params = ep.unmarshal_alarm_token_query(query_params)
        except ValueError as exc:
            log.info("Bad params to shared alarm group token endpoint", exc_info=exc)
            api_resp["status"] = HTTPStatus.BAD_REQUEST.value
            api_resp["message"] = "Bad query to shared alarm group token endpoint"
            return (api_resp, HTTPStatus.BAD_REQUEST.value)
        
        api_resp["payload"] = {
            "accessToken": get_shared_alarm_group_token(
                alarm_group_id,
                params["duration_hours"],
                self._jwt_secret,
            )
        }
        api_resp["message"] = "Processed successfully"
        api_resp["status"] = HTTPStatus.OK.value
        return api_resp, HTTPStatus.OK.value

    @guard_shared_endpoint
    def get_shared_alarm_group(self, shared_alarm_group_params: dict, token_params: dict = None):
        api_resp: typing.Dict[str, typing.Any] = {}
        try:
            shared_token = shared_alarm_group_params.get("sharedToken")
            if not shared_token:
                api_resp["status"] = HTTPStatus.BAD_REQUEST.value
                api_resp["message"] = "Missing shared token"
                return api_resp, HTTPStatus.BAD_REQUEST.value

            # For now, return a simple response - this would need to be implemented
            # to fetch actual alarm group data using the decoded token
            api_resp["payload"] = {"message": "Shared alarm group access"}
            api_resp["message"] = "Processed successfully"
            api_resp["status"] = HTTPStatus.OK.value
            return api_resp, HTTPStatus.OK.value
        except Exception as exc:
            log.error("Error getting shared alarm group", exc_info=exc)
            api_resp["status"] = HTTPStatus.INTERNAL_SERVER_ERROR.value
            api_resp["message"] = "Internal server error"
            return api_resp, HTTPStatus.INTERNAL_SERVER_ERROR.value

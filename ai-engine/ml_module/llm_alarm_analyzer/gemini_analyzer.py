import base64
import json
import mimetypes
import os
import random
import re
import time
import typing
from dataclasses import dataclass, field
from datetime import datetime
from typing import Dict, Optional

import requests
import structlog
from requests.exceptions import ReadTimeout, RequestException, Timeout

from common_utils.metrics_definitions import (
    RG_LLM_API_REQUEST_DURATION_SECONDS,
    RG_LLM_API_RESPONSES,
    RG_LLM_API_RETRIES,
    RG_LLM_API_RETRY_COUNTS,
)
from common_utils.time_utils import format_utc_to_local_with_weekday

log = structlog.get_logger("hakimo", module="Gemini Alarm Analyzer")


@dataclass
class AIResultRA:
    success: bool = False
    reason: Optional[str] = None
    analysis: Optional[str] = None
    recommendation: Optional[str] = None
    input_tokens: Optional[int] = None
    output_tokens: Optional[int] = None


@dataclass
class AIResultLA:
    success: bool = False
    reason: Optional[str] = None
    explanation: Optional[str] = None
    summary: Optional[str] = None
    recommendation: Optional[str] = None
    score: Optional[int] = None
    input_tokens: Optional[int] = None
    output_tokens: Optional[int] = None


@dataclass
class LLMRequestResponse:
    success: bool = False
    data: Dict[str, str] = field(default_factory=dict)
    full_output: str = ""
    input_tokens: Optional[int] = None
    output_tokens: Optional[int] = None
    error_reason: Optional[str] = None


def calculate_retry_sleep_time(
    retry_count: int,
    base_delay: float,
    max_delay: float,
    jitter_factor: float,
) -> float:
    """
    Calculate sleep time for retry mechanism with exponential backoff and jitter.

    Args:
        retry_count: Current retry attempt number (1-based)
        base_delay: Base delay in seconds (default: 1.0)
        max_delay: Maximum delay cap in seconds (default: 8.0)
        jitter_factor: Maximum jitter as a fraction of delay (default: 0.3 = 30%)

    Returns:
        Sleep time in seconds
    """
    # Calculate exponential backoff: base_delay * (2 ^ (retry_count - 1))
    delay = min(max_delay, base_delay * (2 ** (retry_count - 1)))

    # Add random jitter (up to jitter_factor of the delay)
    jitter = random.uniform(0, delay * jitter_factor)

    return delay + jitter


class GeminiAlarmAnalyzer:
    def __init__(
        self,
        api_key: str,
        max_retries: int = 3,
        base_delay: float = 1.0,
        max_delay: float = 3.0,
        jitter_factor: float = 1.0,
    ):
        self.api_key = api_key
        self.base_url = "https://generativelanguage.googleapis.com"
        self.timeout = 15

        # Retry mechanism parameters
        self.max_retries = max_retries
        self.base_delay = base_delay
        self.max_delay = max_delay
        self.jitter_factor = jitter_factor

    def send_request(
        self,
        tenant_id: typing.Optional[str],
        alarm_phase: typing.Optional[str],
        request_text: typing.Optional[str],
        video_uri: typing.Optional[str] = None,
        video_bytes: typing.Optional[bytes] = None,
        images: typing.Optional[typing.List[str]] = None,
        image_mime_type: str = "image/webp",
        temperature: float = 0.1,
    ) -> LLMRequestResponse:
        request_parts = []
        if request_text:
            request_parts.append({"text": request_text})
        if video_uri:
            request_parts.append(
                {
                    "file_data": {
                        "mime_type": "video/mp4",
                        "file_uri": video_uri,
                    }
                }
            )
        if video_bytes:
            request_parts.append(
                {
                    "inline_data": {
                        "mime_type": "video/mp4",
                        "data": video_bytes,
                    }
                }
            )
        if images:
            for image_data in images:
                request_parts.append(
                    {
                        "inline_data": {
                            "mime_type": image_mime_type,
                            "data": image_data,
                        }
                    }
                )
        content_request = {
            "generationConfig": {"temperature": temperature},
            "contents": [
                {
                    "parts": request_parts,
                }
            ],
        }
        generate_headers = {"Content-Type": "application/json"}

        # List of HTTP status codes that should trigger a retry
        retryable_status_codes = [429, 500, 502, 503, 504]
        retry_count = 0
        response_data = None
        response_text = None
        generate_response = None
        request_start_time = time.time()

        # Initialize token count variables
        input_tokens = None
        output_tokens = None

        while True:
            # Initialize error variables for this iteration
            error_type = None
            error_details = None

            try:
                # Make the API request
                generate_response = requests.post(
                    f"{self.base_url}/v1beta/models/gemini-2.0-flash:generateContent?key={self.api_key}",
                    headers=generate_headers,
                    json=content_request,
                    timeout=self.timeout,
                )
                response_text = generate_response.text
                # Parse the JSON response in the same try block to catch both network and JSON errors
                if generate_response.status_code == 200:
                    response_data = generate_response.json()
                    # Successfully received and parsed the response
                    break  # Exit the retry loop

                # HTTP error handling - retryable status codes
                if (
                    generate_response.status_code in retryable_status_codes
                    and retry_count < self.max_retries
                ):
                    status = generate_response.status_code
                    error_type = f"http_{status}"
                    error_details = f"status code {status}"
                    response_text = None  # No response text for HTTP errors
                else:
                    # Non-retryable HTTP error or max retries exceeded
                    log.error(
                        "Gemini API request failed",
                        status_code=generate_response.status_code,
                        response=generate_response.text,
                    )
                    return LLMRequestResponse(
                        success=False,
                        data={},
                        full_output=response_text,
                        error_reason="http_failure",
                    )

            except json.JSONDecodeError as json_err:
                # JSON parsing error
                if retry_count < self.max_retries:
                    error_type = "malformed JSON"
                    error_details = str(json_err)
                    # Add partial response text for debugging
                    if generate_response is not None and hasattr(
                        generate_response, "text"
                    ):
                        response_text = generate_response.text[:200]
                    else:
                        response_text = None
                else:
                    # Max retries exceeded
                    log.error(
                        "Failed to parse Gemini API response after max retries",
                        error=str(json_err),
                        max_retries=self.max_retries,
                    )
                    return LLMRequestResponse(
                        success=False,
                        data={},
                        full_output=response_text or "",
                        error_reason="json_decode_failure",
                    )

            except (RequestException, Timeout, ReadTimeout) as e:
                # Network/connection error
                if retry_count < self.max_retries:
                    if isinstance(e, Timeout):
                        error_type = "timeout"
                    elif isinstance(e, ReadTimeout):
                        error_type = "read_timeout"
                    else:
                        error_type = "connection_error"
                    error_details = str(e)
                    response_text = None
                else:
                    # Max retries exceeded
                    log.error(
                        "Failed to connect to Gemini API after max retries",
                        error=str(e),
                        max_retries=self.max_retries,
                    )
                    return LLMRequestResponse(
                        success=False,
                        data={},
                        full_output=response_text or "",
                        error_reason="connection_failure",
                    )

            # Common retry handling for all error types
            # Only proceed if we have error details (meaning we should retry)
            if error_type is None or error_details is None:
                # This shouldn't happen, but if it does, break to avoid infinite loop
                log.error("Unexpected state: no error details for retry")
                return LLMRequestResponse(
                    success=False,
                    data={},
                    full_output="",
                    error_reason="internal_retry_logic",
                )
            RG_LLM_API_RETRIES.labels(
                tenant=tenant_id,
                alarm_phase=alarm_phase,
                error_type=error_type,
            ).inc()

            retry_count += 1
            sleep_time = calculate_retry_sleep_time(
                retry_count=retry_count,
                base_delay=self.base_delay,
                max_delay=self.max_delay,
                jitter_factor=self.jitter_factor,
            )

            # Log the retry with the appropriate error type
            log_args = {
                "retry_count": retry_count,
                "max_retries": self.max_retries,
                "error": error_details,
                "delay_seconds": sleep_time,
            }

            # Only add response_text if it exists and is not None
            if response_text is not None:
                log_args["response_text"] = response_text

            log.warning(
                f"Gemini API {error_type}, retrying full request", **log_args
            )

            time.sleep(sleep_time)

        status_code = generate_response.status_code
        duration = time.time() - request_start_time

        try:
            RG_LLM_API_REQUEST_DURATION_SECONDS.labels(
                tenant=tenant_id,
                alarm_phase=alarm_phase,
            ).observe(duration)

            RG_LLM_API_RESPONSES.labels(
                tenant=tenant_id,
                alarm_phase=alarm_phase,
                status_code=str(status_code),
            ).inc()

            RG_LLM_API_RETRY_COUNTS.labels(
                tenant=tenant_id,
                alarm_phase=alarm_phase,
            ).observe(retry_count)

        except Exception as e:
            log.warning(
                "Failed to update Gemini API metrics",
                tenant_id=tenant_id,
                alarm_phase=alarm_phase,
                error=str(e),
            )
        # Extract token counts if available
        if response_data and "usageMetadata" in response_data:
            input_tokens = response_data.get("usageMetadata", {}).get(
                "promptTokenCount"
            )
            output_tokens = response_data.get("usageMetadata", {}).get(
                "candidatesTokenCount"
            )

        out = [
            part["text"]
            for candidate in response_data.get("candidates", [])
            for part in candidate.get("content", {}).get("parts", [])
        ]
        full_out = "\n".join(out)

        # Strip leading/trailing spaces and remove markdown markers
        cleaned_output = re.sub(r"```json\n?|```", "", full_out).strip()
        try:
            out_dict = json.loads(cleaned_output)
        except json.JSONDecodeError as e:
            log.error(
                "JSON decoding of Gemini output failed",
                json_error=str(e),
                cleaned_output=cleaned_output,
            )
            return LLMRequestResponse(
                success=False,
                data={},
                full_output=full_out,
                input_tokens=input_tokens,
                output_tokens=output_tokens,
                error_reason="response_parse_error",
            )

        return LLMRequestResponse(
            success=True,
            data=out_dict,
            full_output=full_out,
            input_tokens=input_tokens,
            output_tokens=output_tokens,
        )

    def upload_video(self, video_path: str) -> typing.Tuple[bool, str]:
        mime_type = (
            mimetypes.guess_type(video_path)[0] or "application/octet-stream"
        )
        num_bytes = os.path.getsize(video_path)
        display_name = os.path.basename(video_path)

        # Step 1: Start resumable upload request
        data = {"file": {"display_name": display_name}}
        headers = {
            "X-Goog-Upload-Protocol": "resumable",
            "X-Goog-Upload-Command": "start",
            "X-Goog-Upload-Header-Content-Length": str(num_bytes),
            "X-Goog-Upload-Header-Content-Type": mime_type,
            "Content-Type": "application/json",
        }
        response = requests.post(
            f"{self.base_url}/upload/v1beta/files?key={self.api_key}",
            headers=headers,
            json=data,
        )

        upload_url = response.headers.get("X-Goog-Upload-URL")
        if not upload_url:
            log.error("Failed to get upload URL")
            return False, None

        # Step 2: Upload the video
        with open(video_path, "rb") as video_file:
            upload_headers = {
                "Content-Length": str(num_bytes),
                "X-Goog-Upload-Offset": "0",
                "X-Goog-Upload-Command": "upload, finalize",
            }
            upload_response = requests.post(
                upload_url, headers=upload_headers, data=video_file
            )

        file_info = upload_response.json()
        file_uri = file_info.get("file", {}).get("uri")
        file_name = file_info.get("file", {}).get("name")
        state = file_info.get("file", {}).get("state")

        # Step 3: Ensure the state of the video is 'ACTIVE'
        retry_number = 0
        while state == "PROCESSING" and retry_number < 5:
            time.sleep(1)
            check_response = requests.get(
                f"{self.base_url}/v1beta/{file_name}?key={self.api_key}"
            )
            retry_number += 1
            state = check_response.json().get("state")
        if state != "ACTIVE":
            log.error(
                "Gemini video processing failed or is incomplete.",
                retry_number=retry_number,
                file_name=file_name,
            )
            return False, None
        return True, file_uri

    def send_request_with_video(
        self,
        tenant_id: typing.Optional[str],
        text: str,
        video_path: str,
        use_video_uri: bool = False,
        temperature: float = 0.1,
    ) -> LLMRequestResponse:
        file_uri, video_bytes = None, None
        if use_video_uri:
            upload_success, file_uri = self.upload_video(video_path)
            if not upload_success:
                return LLMRequestResponse(error_reason="upload_failed")
        else:
            with open(video_path, "rb") as video_file:
                video_bytes = base64.b64encode(video_file.read()).decode(
                    "utf-8"
                )
        llm_response: LLMRequestResponse = self.send_request(
            tenant_id=tenant_id,
            alarm_phase="raw",
            request_text=text,
            video_uri=file_uri,
            video_bytes=video_bytes,
            temperature=temperature,
        )
        return llm_response

    def analyze_location_alarm(
        self,
        tenant_id: typing.Optional[str],
        previous_analysis: typing.List[typing.Dict[str, str]],
        sop: typing.Optional[str],
        location_timezone: str,
        temperature: float = 0.1,
    ) -> AIResultLA:
        try:
            prompt_text = [
                "This is a site that is being monitored by a remote security operator. "
                f"The local timezone for the site is {location_timezone}. "
                "Following are the details of raw video events detected by the cameras on the site "
                "along with an explanation of what is happening in those videos:\n"
            ]
            for entry in previous_analysis:
                prompt_text.append(entry["text"] + "\n")

            prompt_text.append(
                f"This is the Standard Operating Procedure (SOP) the operator has to follow: \nSOP_START\n{sop}\nSOP_END\n"
                "Based on this information, is there any unauthorized or suspicious activity happening at the site? "
                "Do not assume that any person/vehicle is authorized unless you are absolutely certain. "
                "Provide an overall summary and a recommendation based on all the events on whether to Resolve or Escalate the activity. "
                "If any of the video events are suspicious, the activity should be escalated. "
                "The recommendation should be Resolve if there is nothing suspicious and no further action is needed. The score should be 40."
                "or Escalate if the operator should take further action. "
                "If the activity matches any of the activities mentioned under the emergency situations in the SOP, Escalate the activity with a score of 90. "
                "If the activity matches any of the activities mentioned under the non-emergency situations in the SOP, Escalate the activity with a score of 70. "
                "If the activity is suspicious and doesn't fall into any of the above categories, Escalate the activity with a score of 100. "
                "Respond in json format with keys 'explanation', 'summary', 'recommendation' and 'score' where"
                "explanation should be a detailed explanation of the activity, summary should be a short summary of the activity, "
                "recommendation should be either Resolve or Escalate based on the explanation and score should be a number between 0 and 100, as given in the cases above."
            )
            prompt_text = "".join(prompt_text)
            log.debug("Gemini Prompt", prompt_text=prompt_text)

            llm_response: LLMRequestResponse = self.send_request(
                tenant_id=tenant_id,
                alarm_phase="location",
                request_text=prompt_text,
                temperature=temperature,
            )
            if not llm_response.success:
                return AIResultLA(
                    reason="api_error",
                    input_tokens=llm_response.input_tokens,
                    output_tokens=llm_response.output_tokens,
                )
            loc_explanation = llm_response.data.get("explanation")
            loc_summary = llm_response.data.get("summary")
            loc_reco = llm_response.data.get("recommendation")
            loc_score = llm_response.data.get("score")
            if loc_score is not None:
                try:
                    loc_score = int(loc_score)
                except (ValueError, TypeError):
                    log.warning("loc_score is not an int", loc_score=loc_score)
                    loc_score = None
            log.info(
                "Gemini analysis of location alarm complete",
                full_output=llm_response.full_output,
                explanation=loc_explanation,
                summary=loc_summary,
                loc_reco=loc_reco,
                loc_score=loc_score,
            )
            return AIResultLA(
                success=True,
                reason=None,
                explanation=loc_explanation,
                summary=loc_summary,
                recommendation=loc_reco,
                score=loc_score,
                input_tokens=llm_response.input_tokens,
                output_tokens=llm_response.output_tokens,
            )

        except Exception:
            return AIResultLA(reason="analysis_error")

    def analyze_alarm(
        self,
        tenant_id: typing.Optional[str],
        video_path: str,
        video_times: typing.Tuple[datetime, datetime],
        sop: typing.Optional[str],
        camera_name: str,
        location_timezone: str,
        send_inline_video: bool = False,
        send_inline_frames: bool = False,
        temperature: float = 0.1,
        frames: typing.Optional[typing.List[str]] = None,
    ) -> AIResultRA:
        vs_str = format_utc_to_local_with_weekday(
            video_times[0], location_timezone
        )
        ve_str = format_utc_to_local_with_weekday(
            video_times[1], location_timezone
        )
        start_time = time.time()

        # Prepare prompt text
        if send_inline_frames and frames is not None:
            input_description = (
                "These are image frames from the camera feed of a site monitored by a remote security operator. "
                "The frames are captured at 1 frame per second whenever motion is detected. "
                f"You are provided a sequence of frames from camera '{camera_name}' at the site. "
                f"The frame capture starts at {vs_str} and ends at {ve_str}. "
            )
        else:
            input_description = (
                f"You are provided a video clip from camera '{camera_name}' at a site monitored by a remote security operator. "
                f"The video starts at {vs_str} and ends at {ve_str}. "
            )

        prompt_text = (
            "You are an advanced AI security analyst. "
            + input_description
            + f"The local timezone of the site is {location_timezone}. "
            f"This is the Standard Operating Procedure (SOP) that the operator must follow, outlining activities that should be escalated to local security or law enforcement: \nSOP_START\n{sop}\nSOP_END\n"
            "Your task is to review the input carefully and determine if any suspicious or unauthorized activity is present, as defined by the SOP. "
            "If suspicious persons or moving vehicles are detected, describe their appearance, behavior, and any relevant context. "
            "Is there anything suspicious happening in the video that is mentioned in the Standard Operating Procedure (SOP)? "
            "Is there any unauthorized or suspicious activity happening in the video that is mentioned in the Standard Operating Procedure (SOP)? "
            "Do not assume that any person or vehicle is authorized unless there is clear evidence. "
            "Based on your assessment, provide a clear recommendation to the operator on whether to **Resolve** (if there is nothing suspicious and no further action needed) or **Escalate** (further investigation or action is required). "
            "Respond strictly in JSON format using double quotes for all keys and string values. The JSON must contain only the following two keys:\n"
            '- "explanation": A detailed explanation of your analysis.\n'
            '- "recommendation": One of "Resolve" or "Escalate".\n'
            "Example format:\n"
            '{\n  "explanation": "Some explanation here.",\n  "recommendation": "Resolve"\n}\n'
            "Do not include any extra text, markdown formatting, or code blocks. Your response must be a valid JSON object."
        )
        log.debug("Gemini Prompt", prompt_text=prompt_text)

        try:
            # Handle frames or video
            if send_inline_frames and frames is not None:
                llm_response: LLMRequestResponse = self.send_request(
                    tenant_id=tenant_id,
                    alarm_phase="raw",
                    request_text=prompt_text,
                    images=frames,
                    image_mime_type="image/webp",
                    temperature=temperature,
                )

            else:
                llm_response: LLMRequestResponse = (
                    self.send_request_with_video(
                        tenant_id=tenant_id,
                        text=prompt_text,
                        video_path=video_path,
                        use_video_uri=not send_inline_video,
                        temperature=temperature,
                    )
                )
            if not llm_response.success:
                return AIResultRA(
                    success=False,
                    reason=llm_response.error_reason,
                    analysis=None,
                    recommendation=None,
                    input_tokens=llm_response.input_tokens,
                    output_tokens=llm_response.output_tokens,
                )

            # Process analysis results
            analysis = llm_response.data.get("explanation")
            recommendation = llm_response.data.get("recommendation")

            log.info(
                "Gemini analysis complete",
                llm_analysis_time=time.time() - start_time,
                full_output=llm_response.full_output,
                analysis=analysis,
                recommendation=recommendation,
                input_tokens=llm_response.input_tokens,
                output_tokens=llm_response.output_tokens,
            )

            return AIResultRA(
                success=True,
                analysis=analysis,
                recommendation=recommendation,
                input_tokens=llm_response.input_tokens,
                output_tokens=llm_response.output_tokens,
            )

        except Exception:
            return AIResultRA(reason="analysis_error")

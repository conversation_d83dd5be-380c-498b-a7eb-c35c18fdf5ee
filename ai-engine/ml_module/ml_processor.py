"""ML Processor - service which runs all ML based processing for an alarm
including for the video and for any other statistical modelling
"""

import argparse
import datetime
import os
import signal
import tempfile
import time
import typing
from enum import IntEnum

import opentracing
import prometheus_client as prom
import requests
import structlog
from opentracing.propagation import Format
from structlog.contextvars import bind_contextvars, unbind_contextvars
from urllib3.exceptions import ProtocolError

import controller as ctrl
import errors
import ml_module.alarm_side_effect_processors as side_effects
from common_utils.acs_msg_builder import AcsMsgBuilder
from common_utils.bridge.msg_sender import MsgSender
from common_utils.cloud_config_utils import cloud_config_manager
from common_utils.metrics_definitions import (
    ALARM_COUNT,
    ALARM_PIPELINE_DELAY,
    ALARM_QUEUE_LENGTH,
    TOTAL_ALARM_PROCESSING_TIME,
    VIDEO_COUNT,
    VIDEO_REQUEST_COUNTER,
)
from common_utils.redis.redis_consumer_utils import RedisConsumerUtils
from common_utils.rpc import Rpc, RpcException
from common_utils.tracer import init_jaeger_tracer, trace_method
from common_utils.video_utils import check_video_frames
from config import backend_config as config
from controller.alarm.alarm_controller import AlarmProcessingCriteria
from interfaces.alarm import (
    ALARM_TYPE_INTEGRATED_MOTION,
    ALARM_TYPE_MOTION,
    Alarm,
)
from interfaces.alarm_mapper import AlarmMapper
from interfaces.alarm_state import AlarmState
from interfaces.entities.entity_types import EntityType
from interfaces.tags import Tags
from ml_module.alarm_grouper.grouper import AlarmGrouper
from ml_module.alarm_grouper.interface import AlarmGrouperLocalInterface
from ml_module.alarm_side_effect_processors.base import BaseSideEffectHandler
from ml_module.anomalous_alarms.service.interface import (
    AnomalousAlarmReasons,
    AnomalousAlarmsServiceInterface,
)
from ml_module.detection_classifier import get_llm_classifier
from ml_module.detection_classifier.gemini_base import GeminiBase
from ml_module.ml_alarm_updater import MLAlarmUpdater
from ml_module.ml_interfaces.service_response import MLServiceResponse
from ml_module.ml_service.interface import (
    MLServiceInterface,
    MLServiceRpcInterface,
)
from ml_module.tap_generator import TAPGenerator
from ml_module.utils import download_alarm_video, should_be_processed_by_ai
from ml_module.video_time_analyzer.analyzer import (
    VideoAnalyzerResult,
    VideoTimeAnalyzer,
)
from models_rds.rds_client import RDSClient

log = structlog.get_logger("hakimo", module="ML Processor")


class MLMetrics(IntEnum):
    PROCESSED = 0
    ERROR = 1
    PROCESSED_WITHOUT_TAGS = 2


class MLProcessor:  # pylint: disable=too-many-instance-attributes
    SHUTDOWN = False

    # pylint: disable=too-many-arguments
    def __init__(
        self,
        db: RDSClient,
        controller: ctrl.ControllerMap,
        ml_service: MLServiceInterface,
        alarm_updater: MLAlarmUpdater,
        side_effect_processors: typing.Sequence[BaseSideEffectHandler],
        anomalous_alarms_service: typing.Optional[
            AnomalousAlarmsServiceInterface
        ] = None,
        llm_classifier: typing.Optional[GeminiBase] = None,
    ):
        self._rds_client = db
        self._controller = controller
        self._ml_alarm_updater = alarm_updater
        self._msg_builder = AcsMsgBuilder(
            self._rds_client.db_adapter,
            MsgSender(self._controller.msg),
        )
        self._alarm_mapper = AlarmMapper(controller)
        self._video_analyzer = VideoTimeAnalyzer(
            self._alarm_mapper, controller
        )
        self._ml_service = ml_service
        self._llm_classifier = llm_classifier
        self._anomalous_alarms_service = anomalous_alarms_service
        self._tap_generator = TAPGenerator(
            self._alarm_mapper, self._rds_client
        )
        self._side_effects = side_effect_processors
        self.metrics: typing.Dict[str, typing.Any] = {
            "video_request_counter": VIDEO_REQUEST_COUNTER,
            "ALARM_QUEUE_LENGTH": ALARM_QUEUE_LENGTH,
            "VIDEO_COUNT": VIDEO_COUNT,
            "ALARM_COUNT": ALARM_COUNT,
            "TOTAL_ALARM_PROCESSING_TIME": TOTAL_ALARM_PROCESSING_TIME,
            "ALARM_PIPELINE_DELAY": ALARM_PIPELINE_DELAY,
        }
        self._redis_consumer_utils = RedisConsumerUtils()

    def process_alarm(self, new_alarm: Alarm) -> AlarmState:
        """
        process_alarm handles the following steps:
        1) Determine type of alarm and action to be taken
        2) Call internal AI modules to get required outputs
        -> Optionally query RDS for details about other alarms
        3) Write outputs to Hakimo database
        """
        try:
            bind_contextvars(
                alarm_id=new_alarm.alarm_uuid,
                alarm_type=new_alarm.alarm_type,
                tenant_id=new_alarm.tenant_id,
                video_path=new_alarm.video_path,
            )
            (
                true_alarm_probability,
                tags,
                new_state,
                ml_service_response,
            ) = self.get_tap_tags(new_alarm)
            log.info(
                "ML Output",
                true_alarm_probability=true_alarm_probability,
                tags=tags,
            )
            (old_tap, old_tags) = self._controller.alarm.get_tap_tags(
                new_alarm.alarm_uuid, new_alarm.tenant_id
            )
            self._ml_alarm_updater.update_ml_outputs(
                new_alarm,
                tags,
                true_alarm_probability,
                new_state,
                ml_service_response,
            )

            self._controller.alarm.transition_alarm(
                new_alarm.alarm_uuid,
                new_alarm.tenant_id,
                new_state,
                old_state_check=AlarmState.IN_PROGRESS,
            )
            self._process_alarm_side_effects(
                new_alarm,
                tags,
                true_alarm_probability,
                old_tags,
                old_tap,
                new_state,
                ml_service_response,
            )
            # note that this state might not be accurate if
            # alarm state was changed manually during processing
            return new_state
        finally:
            unbind_contextvars(
                "alarm_id", "tenant_id", "video_path", "alarm_type"
            )

    @staticmethod
    def aggregate_outputs(
        output_1: typing.Tuple[
            float, Tags, typing.Optional[MLServiceResponse]
        ],
        output_2: typing.Tuple[
            float, Tags, typing.Optional[MLServiceResponse]
        ],
    ) -> typing.Tuple[float, Tags, typing.Optional[MLServiceResponse]]:
        """Aggregate the tap, tags and new alarm state from different sources
        WARNING: modifies output_1 tags in place"""
        out_tap = max(output_1[0], output_2[0])
        output_1[1].merge(output_2[1])
        out_resp = None
        if output_1[2] is None and output_2[2] is not None:
            out_resp = output_2[2]
        elif output_2[2] is None and output_1[2] is not None:
            out_resp = output_1[2]
        elif output_1[2] is not None and output_2[2] is not None:
            out_resp = MLServiceResponse.merge(output_1[2], output_2[2])

        return (out_tap, output_1[1], out_resp)

    def _add_anomalous_tags(
        self,
        tags: Tags,
        if_anomalous: bool,
        anomalous_reason: AnomalousAlarmReasons,
    ) -> Tags:
        if if_anomalous:
            if anomalous_reason == AnomalousAlarmReasons.ANOMALOUS_HOUR:
                tags.add_video_tag("ANOMALOUS_ENTRY", 100)
            elif (
                anomalous_reason
                == AnomalousAlarmReasons.ANOMALOUS_HOUR_AFTER_HOURS
            ):
                tags.add_video_tag("ANOMALOUS_ENTRY_AFTER_HOURS", 100)
            elif (
                anomalous_reason
                == AnomalousAlarmReasons.EMPLOYEE_UNSEEN_IN_LAST_2_WEEKS
            ):
                tags.add_video_tag(
                    "ANOMALOUS_ENTRY_PERSON_UNSEEN_LAST_2_WEEKS", 100
                )
        return tags

    @trace_method
    def _process_integrated_motion_alarm(
        self, alarm: Alarm
    ) -> typing.Tuple[
        typing.Optional[float],
        Tags,
        AlarmState,
        typing.Optional[MLServiceResponse],
    ]:
        log.info("Assigning Default TAP, TAG to integrated motion alarm")
        return 90.0, Tags([], {"tags": []}), AlarmState.PROCESSED, None

    @trace_method
    def get_tap_tags(
        self, new_alarm: Alarm
    ) -> typing.Tuple[
        typing.Optional[float],
        Tags,
        AlarmState,
        typing.Optional[MLServiceResponse],
    ]:
        """
        For alarm_type == Integrated Motion, we don't kickoff any processing.
        we just return with TAP and TAG.
        """

        if new_alarm.alarm_type == ALARM_TYPE_INTEGRATED_MOTION:
            return self._process_integrated_motion_alarm(new_alarm)
        t_config = self._controller.tenant.get_config(
            tenant_id=new_alarm.tenant_id
        )
        if t_config is not None and t_config.skipMLProcessing:
            log.warning(
                "ML Processing skipped for tenant_id: {}".format(
                    new_alarm.tenant_id
                )
            )
            return 75.0, Tags([], {"tags": []}), AlarmState.PROCESSED, None

        if new_alarm.hakimo_correlation_id is not None:
            # Short circuit the processing in this case
            tap, tags = self._controller.alarm.get_tap_tags(
                new_alarm.hakimo_correlation_id, new_alarm.tenant_id
            )
            log.info("Using output of correlated event")
            # If output not available, switch to UNPROCESSED and hope event is
            # picked up eventually when the correlated event output is
            # available
            if tap is None or tags is None:
                log.warning("No output for correlated event!")
                # None tap is ok, None tags is not. If tap is None, then no
                # ML Output is created
                return (
                    tap,
                    Tags([], {"tags": []}),
                    AlarmState.UNPROCESSED,
                    None,
                )
            return tap, tags, AlarmState.PROCESSED, None
        with tempfile.TemporaryDirectory() as tmp_dir:
            video_details = self._get_video_details(new_alarm, tmp_dir)
            tap = 0
            tags = Tags([], {})
            ml_response = None
            original_out = (tap, tags, ml_response)
            if (
                self._anomalous_alarms_service is not None
                and self._alarm_mapper.get_internal_alarm_type(
                    new_alarm
                ).startswith("Access Granted")
            ):
                try:
                    (
                        is_anomalous,
                        reason,
                    ) = self._anomalous_alarms_service.classify(
                        new_alarm, check_time=True
                    )
                    tags = self._add_anomalous_tags(tags, is_anomalous, reason)
                except Exception:  # pylint: disable=broad-except
                    log.error("Exception in anomalous alarms")
            (
                enough_video,
                video_request_time,
            ) = self._video_analyzer.verify_sufficient_video(
                new_alarm,
                video_details,
            )
            if (
                new_alarm.process_people
                and new_alarm.process_vehicles
                and (
                    t_config is not None
                    and t_config.useEnsembleDetector is True
                )
            ):
                tap, tags, ml_response = self.aggregate_outputs(
                    original_out,
                    self.get_tap_for_entity(
                        new_alarm,
                        video_details,
                        (EntityType.PERSON, EntityType.VEHICLE),
                        useLLMPeopleClassifier=t_config.useLLMClassifier,
                        useLLMVehicleClassifier=t_config.useLLMClassifierVehicles,
                    ),
                )
            else:
                if new_alarm.process_people:
                    tap, tags, ml_response = self.aggregate_outputs(
                        original_out,
                        self.get_tap_for_entity(
                            new_alarm,
                            video_details,
                            EntityType.PERSON,
                            useLLMPeopleClassifier=t_config.useLLMClassifier,
                            useLLMVehicleClassifier=t_config.useLLMClassifierVehicles,
                        ),
                    )
                if new_alarm.process_vehicles:
                    tap, tags, ml_response = self.aggregate_outputs(
                        (tap, tags, ml_response),
                        self.get_tap_for_entity(
                            new_alarm,
                            video_details,
                            EntityType.VEHICLE,
                            useLLMPeopleClassifier=t_config.useLLMClassifier,
                            useLLMVehicleClassifier=t_config.useLLMClassifierVehicles,
                        ),
                    )
            alarm_state = self._request_more_video(
                new_alarm, video_details, enough_video, video_request_time
            )

            return tap, tags, alarm_state, ml_response

    def _request_more_video(
        self,
        alarm: Alarm,
        video_details: dict,
        enough_video: VideoAnalyzerResult,
        video_request_time: typing.Tuple[
            typing.Optional[datetime.datetime],
            typing.Optional[datetime.datetime],
        ],
    ) -> AlarmState:
        alarm_state = AlarmState.PROCESSED

        log.info(
            "Video check complete",
            enough_video=enough_video,
            video_start_time_utc=video_request_time[0],
            video_end_time_utc=video_request_time[1],
        )
        if enough_video == VideoAnalyzerResult.NOT_ENOUGH:
            log.info(
                "Not enough video. Sending message for more video",
                start_time=video_request_time[0],
                end_time=video_request_time[1],
            )
            self._controller.alarm.send_video_request(
                alarm,
                start_time=video_request_time[0],
                end_time=video_request_time[1],
            )
            alarm_state = AlarmState.VIDEO_REQUESTED
            # Asking for more video
            self.metrics["video_request_counter"].labels(
                tenant=alarm.tenant_id, request_type="subsequent"
            ).inc()
        elif video_details.get("video_corrupt"):
            if (
                self._controller.alarm.by_id(
                    alarm.alarm_uuid
                ).video_corrupt_retries
                < config.HAIE.videoProps["corruptRetries"]
            ):
                # If we have some more retries and the video is corrupt,
                # then rerequest the video
                log.info("Rerequesting corrupt video")
                self._controller.alarm.inc_alarm_video_corrupt(
                    alarm.alarm_uuid, alarm.tenant_id
                )
                self._controller.alarm.send_video_request(
                    alarm,
                    start_time=video_request_time[0],
                    end_time=video_request_time[1],
                )
                alarm_state = AlarmState.VIDEO_REQUESTED
                self.metrics["video_request_counter"].labels(
                    tenant=alarm.tenant_id, request_type="subsequent"
                ).inc()
            else:
                alarm_state = AlarmState.PROCESSED
        # Update processed start and end time in the DB
        self._controller.alarm.update_alarm_processed_times(
            alarm, video_request_time[0], video_request_time[1]
        )
        return alarm_state

    def get_tap_for_entity(
        self,
        new_alarm: Alarm,
        video_details: typing.Dict,
        entity: typing.Union[EntityType, typing.Tuple[EntityType, EntityType]],
        useLLMPeopleClassifier: bool = False,
        useLLMVehicleClassifier: bool = False,
    ) -> typing.Tuple[float, Tags, typing.Optional[MLServiceResponse]]:
        """Get the tap and tags when looking for people and their interaction
        with the scene.
        """
        motion = None
        ml_resp = None
        alarm_processing_config = self._controller.alarm.get_processing_config(
            new_alarm
        )
        if alarm_processing_config is None:
            raise ValueError("Cannot find processing config for alarm")
        if (
            # Does video exist?
            video_details.get("local_video_path") is not None
            # Is the door labelled when not a motion alarm
            and (
                new_alarm.scene_info is not None
                or new_alarm.alarm_type == ALARM_TYPE_MOTION
            )
            # If video exists should we process
            and should_be_processed_by_ai(
                alarm_processing_config,
                video_details,
                new_alarm,
                self._alarm_mapper.get_internal_alarm_type(new_alarm),
            )
        ):
            ml_resp = self._ml_service.get_entities(new_alarm, entity)
            entities, motion = (
                ml_resp.entities,
                ml_resp.motion,
            )
            if ml_resp is not None and self._llm_classifier is not None:
                people = [
                    entity
                    for entity in entities
                    if entity._ENT_TYPE == EntityType.PERSON
                ]
                vehicles = [
                    entity
                    for entity in entities
                    if entity._ENT_TYPE == EntityType.VEHICLE
                ]
                if useLLMPeopleClassifier:
                    people = self._llm_classifier.classify_people(
                        people, new_alarm.tenant_id, new_alarm.source_entity_id
                    )
                if useLLMVehicleClassifier:
                    vehicles = self._llm_classifier.classify_vehicles(
                        vehicles,
                        new_alarm.tenant_id,
                        new_alarm.source_entity_id,
                    )
                entities = people + vehicles

        elif video_details.get("local_video_path") is None:
            log.info("No video for alarm!")
            video_details["local_video_path"] = None
            entities = []
        else:
            # Alarm is not processed, but video exists
            log.info("Alarm not processed by AI")
            entities = []

        log.info(
            "Checking if enough video",
            alarm_time=new_alarm.alarm_time,
            video_start_time_utc=new_alarm.video_start_time_utc,
            video_end_time_utc=new_alarm.video_end_time_utc,
            video_details=video_details,
        )

        true_alarm_probability, tags = self._tap_generator.get_tap(
            entities,
            new_alarm,
            video_details,
            alarm_processing_config,
            motion=motion,
        )
        if ml_resp and ml_resp.matched_faces:
            tags.add_video_tag("FACE_RECOGNIZED", 100)
        return true_alarm_probability, tags, ml_resp

    def run(
        self,
        alarm_criteria: AlarmProcessingCriteria,
    ):
        """
        Single process that runs the direction classifier and uploads
        ML outputs to RDS
        """
        is_queue_based_polling_active = cloud_config_manager.is_enabled(
            "queue_based_polling.is_enabled"
        )
        queue_based_polling_tenants = cloud_config_manager._feature_flag.get(
            "queue_based_polling.active_tenants", []
        )
        message = None
        while not self.SHUTDOWN:
            alarm_processed_successfully = True
            try:
                if (
                    is_queue_based_polling_active
                    and alarm_criteria.queue_processor
                ):
                    message, alarm_details, older_alarm_details = (
                        self._controller.alarm.get_alarm_to_process_from_queue(
                            alarm_criteria,
                        )
                    )
                else:
                    alarm_details = (
                        self._controller.alarm.get_alarm_to_process(
                            alarm_criteria,
                            is_queue_based_polling_active,
                            queue_based_polling_tenants,
                        )
                    )
                # Polling this stats from grafana periodically
                # if (
                #     is_queue_based_polling_active
                #     and alarm_criteria.queue_processor
                # ):
                #     count_by_tenant = self._redis_consumer_utils.get_alarm_counts_per_tenant_from_redis()
                # else:
                #     count_by_tenant = self._controller.alarm.get_alarm_counts_group_by_tenant(
                #         AlarmState.UNPROCESSED
                #     )
                # for tenant, count in count_by_tenant.items():
                #     self.metrics["ALARM_QUEUE_LENGTH"].labels(tenant).set(
                #         count
                #     )
            except errors.RetryableError as exc:
                log.debug(
                    "Error fetching alarm from the DB. Retrying", exc_info=exc
                )
                continue
            if alarm_details[0] is not None and alarm_details[1] is not None:
                try:
                    log.info(
                        "======== PROCESSING ALARM",
                        alarm_id=alarm_details[0],
                    )

                    process_current_row_start_time = time.time()
                    alarm = self._rds_client.get_alarm_object(
                        alarm_details[0], alarm_details[1]
                    )
                    assert alarm is not None
                    if alarm.trace_data is not None:
                        parent_span = opentracing.tracer.extract(
                            Format.TEXT_MAP, alarm.trace_data
                        )
                    else:
                        parent_span = None
                    with opentracing.tracer.start_active_span(
                        "ml-processing",
                        child_of=parent_span,
                        tags={
                            "alarm_id": alarm.alarm_uuid,
                            "alarm_type": alarm.alarm_type,
                        },
                    ):
                        alarm_state = self.process_alarm(alarm)
                        if (
                            alarm_state
                            and is_queue_based_polling_active
                            and alarm_criteria.queue_processor
                            and alarm_state == AlarmState.VIDEO_REQUESTED
                            and message
                        ):
                            alarm_processed_successfully = False

                    proc_time = time.time() - process_current_row_start_time
                    alarm_pipeline_delay = (
                        int(time.time()) - alarm.alarm_time.timestamp()
                    )
                    log.info(
                        "Time to process current alarm",
                        alarm_id=alarm.alarm_uuid,
                        alarm_type=alarm.alarm_type,
                        processing_time=proc_time,
                        tenant_id=alarm.tenant_id,
                        alarm_pipeline_delay=alarm_pipeline_delay,
                    )
                    self.metrics["TOTAL_ALARM_PROCESSING_TIME"].labels(
                        tenant=alarm_details[1]
                    ).observe(proc_time)
                    self.metrics["ALARM_COUNT"].labels(
                        tenant=alarm_details[1],
                        result=MLMetrics.PROCESSED.name,
                    ).inc()
                    self.metrics["ALARM_PIPELINE_DELAY"].labels(
                        tenant=alarm_details[1]
                    ).observe(alarm_pipeline_delay)
                except (
                    errors.RetryableError,
                    RpcException,
                    ConnectionResetError,
                    ProtocolError,
                    requests.HTTPError,
                ) as err:
                    log.info(
                        "Retryable exception while processing alarm",
                        exc_info=err,
                        alarm_id=alarm_details[0],
                    )
                    self._controller.alarm.transition_alarm(
                        alarm_details[0],
                        alarm_details[1],
                        AlarmState.UNPROCESSED,
                        old_state_check=AlarmState.IN_PROGRESS,
                    )
                    # Republish alarm to the queue
                    if (
                        alarm
                        and is_queue_based_polling_active
                        and alarm_criteria.queue_processor
                        and message
                    ):
                        alarm_processed_successfully = False
                except Exception as ex:  # pylint: disable=broad-except
                    log.exception(
                        "Error in Alarm Processing",
                        exc_info=ex,
                        alarm_id=alarm_details[0],
                        tenant_id=alarm_details[1],
                    )
                    self._controller.alarm.transition_alarm(
                        alarm_details[0],
                        alarm_details[1],
                        AlarmState.ERROR,
                    )
                    self.metrics["ALARM_COUNT"].labels(
                        tenant=alarm_details[1], result=MLMetrics.ERROR.name
                    ).inc()
            elif alarm_details[2]:
                # This is the case when alarm controller returns with skip_processing = True
                # We don't wait in this case
                if (
                    alarm_criteria.queue_processor
                    and message
                    and alarm_processed_successfully
                ):
                    self._redis_consumer_utils.ack(message)
                    log.info(
                        "AlarmProcessingLog",
                        event_type="Old Alarm Acknowledged",
                        alarm_id=older_alarm_details[0],
                        tenant_id=older_alarm_details[1],
                    )
                continue
            else:
                # polling the queue with a delay of 2 seconds when
                # the queue is empty
                log.debug("No new alarms to process")
                time.sleep(2)
            if (
                alarm_criteria.queue_processor
                and message
                and alarm_processed_successfully
            ):
                try:
                    self._redis_consumer_utils.ack(message)
                except Exception as e:
                    log.info(f"Error while acknowledging the message: {e}")
                log.info(
                    "AlarmProcessingLog",
                    event_type="Alarm Acknowledged",
                    alarm_id=alarm_details[0],
                    tenant_id=alarm_details[1],
                )

        log.info("Shutting down ML Processor")
        for side_effect in self._side_effects:
            if isinstance(
                side_effect, side_effects.LLMAlarmAnalyzerSideEffect
            ):
                # if hasattr(side_effect, "shutdown") and callable(getattr(side_effect, "shutdown")):
                side_effect.shutdown()

    @trace_method
    def _get_video_details(self, alarm: Alarm, temp_dir: str) -> dict:
        """Function to get the video details for an Alarm,
        will check video frames for corrupt, will only return
        local video path if uncorrupt video

        Args:
            alarm ([Alarm]): Alarm object
            tmp_dir ([str]): Temporary working directory to download to

        Returns:
            vid_details ([Dict]): dict with local_video_path,
                bucket_name, file_name, video_available,
                video_corrupt keys
        """
        vid_details: typing.Dict[str, typing.Any] = {}
        # Download video if needed
        if alarm.video_path is None:
            vid_details["local_video_path"] = None
        else:
            vid_details["local_video_path"] = download_alarm_video(
                alarm.video_path, temp_dir
            )
            processing_fps = config.HAIE.PROCESSING_FPS
            if alarm.alarm_type == ALARM_TYPE_MOTION:
                processing_fps = config.HAIE.MOTION_PROCESSING_FPS

            # Assumption is that video is exactly
            # from alarm_time - time_before_alarm
            # to alarm_time + time_after_alarm
            if not check_video_frames(
                vid_details["local_video_path"], processing_fps
            ):
                log.info(
                    "Video corrupt, setting video corrupt and path to None",
                    video_path=vid_details["local_video_path"],
                )
                vid_details["video_corrupt"] = True
                os.unlink(vid_details["local_video_path"])
                vid_details["local_video_path"] = None
                self.metrics["VIDEO_COUNT"].labels(
                    tenant=alarm.tenant_id, corrupt="true"
                ).inc()
            else:
                vid_details["video_corrupt"] = False
                self.metrics["VIDEO_COUNT"].labels(
                    tenant=alarm.tenant_id, corrupt="false"
                ).inc()

        vid_details["video_available"] = alarm.video_available
        return vid_details

    def _process_alarm_side_effects(
        self,
        new_alarm: Alarm,
        new_tags: Tags,
        new_tap: float,
        old_tags: typing.Optional[Tags],
        old_tap: typing.Optional[float],
        new_state: AlarmState,
        ml_response: typing.Optional[MLServiceResponse],
    ):
        processing_config = self._controller.alarm.get_processing_config(
            new_alarm
        )
        for side_effect in self._side_effects:
            try:
                side_effect.handle_side_effect(
                    processing_config,
                    new_alarm,
                    new_tap,
                    new_tags,
                    old_tap,
                    old_tags,
                    new_state,
                    ml_service_response=ml_response,
                )
            except Exception as exc:  # pylint: disable=broad-except
                log.exception(
                    "Exception in alarm processing side effect",
                    exc_info=exc,
                )


def handle_sigterm_ml_processor(signum, stack):  # pylint: disable=unused-argument
    log.info("Received SIGTERM, completing last alarm process")
    MLProcessor.SHUTDOWN = True


def get_ml_processor():
    db = RDSClient()
    cm = ctrl.ControllerMap(db.db_adapter, read_db_adapter=db.read_db_adapter)
    ml_updater = MLAlarmUpdater(cm)
    ml_service = MLServiceRpcInterface(
        Rpc(
            {
                "url": f"http://{config.HAIE.mlService['serviceName']}:"
                f"{config.HAIE.mlService['port']}"
            }
        )
    )
    grouper = AlarmGrouperLocalInterface(AlarmGrouper(cm))
    anom_alarm_service = None
    # anom_alarm_service = AnomalousAlarmsServiceRpcInterface(
    #     Rpc(
    #         {
    #             "url": f"http://{config.HAIE.anomalousAlarmsService['serviceName']}:"
    #             f"{config.HAIE.anomalousAlarmsService['port']}"
    #         }
    #     )
    # )
    llm_classifier = get_llm_classifier()
    return MLProcessor(
        db=db,
        controller=cm,
        ml_service=ml_service,
        alarm_updater=ml_updater,
        side_effect_processors=[
            side_effects.MotionLLMAlarmAnalyzerSideEffect(
                cm, side_effect_order="pre"
            ),
            side_effects.TailgatingSideEffect(db.db_adapter, ml_updater),
            side_effects.AlarmCanceledSideEffect(cm),
            side_effects.CommentGenerator(cm),
            side_effects.UnauthorizedEntrySideEffect(
                db.db_adapter, ml_updater
            ),
            side_effects.MotionDetectionSideEffect(cm),
            side_effects.alarm_grouper.AlarmGrouperSideEffect(grouper),
            side_effects.AlarmNotifierSideEffect(cm),
            side_effects.EverbridgeEventSideEffect(db.db_adapter),
            side_effects.ZoomEventSideEffect(db.db_adapter),
            side_effects.MotionLLMAlarmAnalyzerSideEffect(
                cm, side_effect_order="post"
            ),
            side_effects.EnterpriseLLMAlarmAnalyzerSideEffect(
                cm, side_effect_order="post"
            ),
        ],
        anomalous_alarms_service=anom_alarm_service,
        llm_classifier=llm_classifier,
    )


def main():
    init_jaeger_tracer("ML Processor", hip=False)
    signal.signal(signal.SIGTERM, handle_sigterm_ml_processor)
    prom.start_http_server(8000)
    ml_processor = get_ml_processor()
    args = parse_args()
    alarm_criteria = AlarmProcessingCriteria(
        per_tenant_processor=args.tenant_processor,
        processor_tenants=list(args.tenants),
        enterprise_processor=args.enterprise_processor,
        queue_processor=args.queue_processor,
    )
    if args.with_video:
        alarm_criteria.with_video = True
    elif args.no_video:
        alarm_criteria.with_video = False
    else:
        alarm_criteria.with_video = None
    ml_processor.run(alarm_criteria)


def parse_args():
    parser = argparse.ArgumentParser()
    parser.add_argument(
        "--no_video",
        action="store_true",
        help="Only process alarms without video",
    )
    parser.add_argument(
        "--with_video",
        action="store_true",
        help="Only process alarms with video",
    )
    parser.add_argument(
        "--enterprise_processor",
        default=False,
        help="Whether the processor should process only enterprise tenants",
    )
    parser.add_argument(
        "--tenant_processor",
        default=False,
        help="Whether the processor should run in tenant specific mode",
    )
    parser.add_argument(
        "--tenants",
        default=[],
        nargs="+",
        help="List of tenants to be considered in inclusion and exlusion list",
    )
    parser.add_argument(
        "--queue_processor",
        default=False,
        help="Whether the processor should process only from queue",
    )
    return parser.parse_args()


if __name__ == "__main__":
    main()

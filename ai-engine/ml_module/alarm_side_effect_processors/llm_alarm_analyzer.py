import abc
import json
import subprocess
import tempfile

# import threading
import time
import typing
from collections import defaultdict as dd
from dataclasses import dataclass
from datetime import datetime, timedelta
from multiprocessing import Process, Queue
from pathlib import Path
from uuid import uuid4

import cv2
import numpy as np
import structlog

import controller as ctrl
from common_utils.cloud_config_utils import cloud_config_manager
from common_utils.db_pool import ctrl_pool
from common_utils.ffmpeg_helpers import slow_down_video
from common_utils.metrics_definitions import (
    RG_LLM_LOCATION_ALARM_PROCESSING_SECONDS,
    RG_LLM_LOCATION_ALARM_TOKENS,
    RG_LLM_LOCATION_ALARMS_PROCESSED,
    RG_LLM_RAW_ALARM_PROCESSING_SECONDS,
    RG_LLM_RAW_ALARM_TOKENS,
    RG_LLM_RAW_ALARMS_PROCESSED,
    RG_LLM_TASK_QUEUE_LENGTH,
    RG_LLM_WORKER,
)
from common_utils.time_utils import (
    format_utc_to_local_with_weekday,
    timedelta_to_str,
)
from common_utils.typing_helpers import NUMBER
from common_utils.video_utils import extract_and_encode_frames, get_video_fps
from config import backend_config as config
from controller.alarm.alarm_filters import AlarmFilters
from interfaces.alarm import ALARM_TYPE_MOTION, Alarm, AlarmState
from interfaces.alarm_mapper import AlarmMapper
from interfaces.entities.entity import Entity
from interfaces.location_alarms import (
    LocationAlarmStatus,
    LocationAlarmUpdateType,
)
from interfaces.tags import Tags, TagTypes
from interfaces.tenant_config import (
    AlarmProcessingConfig,
    LLMAlarmAnalyzerConfig,
)
from ml_module.alarm_side_effect_processors.base import BaseSideEffectHandler
from ml_module.llm_alarm_analyzer.gemini_analyzer import (
    AIResultLA,
    AIResultRA,
    GeminiAlarmAnalyzer,
    LLMRequestResponse,
)
from ml_module.ml_interfaces.service_response import MLServiceResponse
from ml_module.tap_generator._unauthorized_entries.utils import (
    deduplicate_list_with_employee_ids,
)
from ml_module.utils import download_alarm_video
from ml_module.visualization.drawing_utils import draw_doors
from models_rds.location_alarms.location_alarm_update import (
    LocationAlarmUpdates,
)
from models_rds.location_alarms.location_alarms import LocationAlarms
from models_rds.raw_alarms import RawAlarms
from models_rds.rds_client import RDSClient

log = structlog.get_logger("hakimo", module="LLM Alarm Analyzer Side Effect")


@dataclass
class LLMAlarmAnalyzerSideEffectResults:
    ra_analysis: typing.Optional[str] = None
    ra_recommendation: typing.Optional[str] = None
    la_score: typing.Optional[int] = None
    la_recommendation: typing.Optional[str] = None
    la_summary: typing.Optional[str] = None
    la_explanation: typing.Optional[str] = None
    la_state: typing.Optional[typing.Dict] = None


class LLMAlarmAnalyzerSideEffect(BaseSideEffectHandler):
    def __init__(
        self,
        controller: ctrl.ControllerMap,
        side_effect_order: typing.Optional[str] = None,
    ):
        self._analyzer = GeminiAlarmAnalyzer(
            api_key=config.HAIE.GEMINI_ALARM_ANALYZER_KEY
        )
        self.side_effect_order = side_effect_order
        self._controller = controller
        self._rds_client = RDSClient(controller.db)
        self.task_queue: Queue = Queue(
            maxsize=config.HAIE.GEMINI_ALARM_ANALYZER_QUEUE_SIZE
        )
        self.task_runners: typing.List[Process] = []
        self._metrics: typing.Dict[str, typing.Any] = {
            "rg_llm_task_queue_length": RG_LLM_TASK_QUEUE_LENGTH,
            "rg_llm_worker": RG_LLM_WORKER,
            "rg_llm_raw_alarms_processed": RG_LLM_RAW_ALARMS_PROCESSED,
            "rg_llm_location_alarms_processed": RG_LLM_LOCATION_ALARMS_PROCESSED,
            "rg_llm_raw_alarm_processing_seconds": RG_LLM_RAW_ALARM_PROCESSING_SECONDS,
            "rg_llm_location_alarm_processing_seconds": RG_LLM_LOCATION_ALARM_PROCESSING_SECONDS,
            "rg_llm_raw_alarm_tokens": RG_LLM_RAW_ALARM_TOKENS,
            "rg_llm_location_alarm_tokens": RG_LLM_LOCATION_ALARM_TOKENS,
        }
        for i in range(config.HAIE.GEMINI_ALARM_ANALYZER_WORKERS):
            p = Process(target=self.take_from_queue)
            p.start()
            self.task_runners.append(p)

        RG_LLM_WORKER.set(config.HAIE.GEMINI_ALARM_ANALYZER_WORKERS)
        RG_LLM_TASK_QUEUE_LENGTH.set(self.task_queue.qsize())

    def take_from_queue(self):
        log.info("Starting Worker")

        while True:
            RG_LLM_TASK_QUEUE_LENGTH.set(self.task_queue.qsize())
            task = self.task_queue.get()
            if task is not None:
                log.info("[Worker] Processing task")
                self.process_alarm(
                    *task,
                )
            else:
                log.info("[Worker] Ending Gemini Worker")
                RG_LLM_WORKER.dec()
                break

    def shutdown(self):
        log.info("[Main] Shuting down task runner")
        for i in range(config.HAIE.GEMINI_ALARM_ANALYZER_WORKERS):
            self.task_queue.put(None)
        for i in range(config.HAIE.GEMINI_ALARM_ANALYZER_WORKERS):
            self.task_runners[i].join()
        RG_LLM_WORKER.set(0)
        RG_LLM_TASK_QUEUE_LENGTH.set(0)
        log.info("[Main] Worker shut down")

    @abc.abstractmethod
    def _should_process(
        self,
        alarm: Alarm,
        new_tap: NUMBER,
        processing_config: typing.Optional[AlarmProcessingConfig],
        new_tags: Tags,
    ) -> bool:
        raise NotImplementedError

    @abc.abstractmethod
    def _use_default_llm_alarm_analyzer_config(
        self,
    ) -> bool:
        raise NotImplementedError

    @abc.abstractmethod
    def _setup_alarm_data(
        self,
        alarm: Alarm,
        analyzeLocationAlarm: bool,
        usePreviousLocationAlarmExplanation: bool,
    ):
        raise NotImplementedError

    @abc.abstractmethod
    def _setup(self, alarm: Alarm):
        raise NotImplementedError

    @abc.abstractmethod
    def process_alarm(
        self,
        alarm: Alarm,
        tags: Tags,
        loc_alarm: typing.Optional[LocationAlarms],
        resolve_raw_alarms: bool,
        escalate_raw_alarms: bool,
        resolve_loc_alarms: bool,
        update_location_alarm_tap: bool,
        video_times: typing.Tuple[datetime, datetime],
        sop: typing.Optional[str],
        camera_name: str,
        location_name: str,
        location_timezone: str,
        previous_analysis: typing.List[typing.Dict[str, str]],
        previous_alarms: typing.List[str],
        analyzeRawAlarm: bool,
        analyzeLocationAlarm: bool,
        send_inline_video: bool,
        send_inline_frames: bool,
        check_threats: bool,
        slow_down_video_flag: bool,
        temperature: float,
    ):
        raise NotImplementedError

    @abc.abstractmethod
    def _should_resolve_raw_alarm(
        self,
        resolveRawAlarm: bool,
        tenant_id: str,
        source_entity_id: str,
        n_entities: int,
    ) -> bool:
        raise NotImplementedError

    def _publish_threat_alarm(self, alarm: Alarm, description: str):
        if "fire" in description.lower():
            alarm_type = "Fire Detected"
            true_alarm_probability = 98
        else:
            alarm_type = "Weapon Detected"
            true_alarm_probability = 99
        alarm_info = {
            "alarm_type": alarm_type,
            "alarm_time": alarm.created_at_utc,
            "source_id": "derived/threat/" + (alarm.source_id or str(uuid4())),
            "source_system": alarm.source_system,
            "tenant_id": alarm.tenant_id,
            "video_path": alarm.video_path,
            "state": AlarmState.PROCESSED,
            "processing_start_time_utc": alarm.processing_start_time_utc,
            "processing_end_time_utc": alarm.processing_end_time_utc,
        }
        if alarm.source_entity_type == "DOOR":
            alarm_info["door_uuid"] = alarm.door_uuid or alarm.source_entity_id
        new_alarm = Alarm(**alarm_info)
        new_alarm.source_entity_type = alarm.source_entity_type
        assert alarm.source_entity_id is not None
        new_alarm.source_entity_id = alarm.source_entity_id
        new_alarm.display = True
        raw_alarm_uuid = self._rds_client.write_update_raw_alarms(new_alarm)
        self._rds_client.update_raw_alarms_with_video_metadata(
            raw_alarm_uuid,
            alarm.video_start_time_utc,
            alarm.video_end_time_utc,
            video_path=alarm.video_path,
        )
        threat_alarm = self._rds_client.get_alarm_object(
            raw_alarm_uuid, alarm.tenant_id
        )
        assert threat_alarm is not None
        with ctrl_pool.get() as controller:  # type: ignore
            controller.alarm_media.add_media(
                raw_alarm_uuid, alarm.video_path, media_type="video"
            )
            controller.alarm.add_comment(
                new_alarm.alarm_uuid,
                new_alarm.tenant_id,
                description,
            )
            controller.alarm.update_tap(
                new_alarm.alarm_uuid,
                new_alarm.tenant_id,
                true_alarm_probability,
            )
            controller.alarm.mark_alarm_pending(threat_alarm)

    def _llm_threat_classify(
        self,
        tenant_id: str,
        alarm_video_path: str,
        tags: Tags,
        do_crop: bool = False,
    ) -> typing.Tuple[bool, str]:
        text = (
            "You are a security agent watching for an threats in a video stream"
            "Look for any person in the video with any weapon such as a gun, knife or any lethal weapons, "
            "if so describe the appearance of the person and at what timestamp the weapon is visible in the summary. "
            "The recommendation should be Resolve if there is nothing suspicious and no further action is needed, "
            "or Escalate if the operator should take further action.\n"
            "Also watch for fire or smoke and Escalate in that scenario as well.\n"
            "Respond in json format with keys 'summary' and 'recommendation' where recommendation is either 'Resolve' or 'Escalate'. "
        )
        if do_crop:
            cropped_alarm_video_path = AlarmEventHandler.crop_video(
                alarm_video_path, tags
            )
        else:
            cropped_alarm_video_path = alarm_video_path

        llm_response: LLMRequestResponse = (
            self._analyzer.send_request_with_video(
                tenant_id=tenant_id,
                text=text,
                video_path=cropped_alarm_video_path,
                use_video_uri=False,
            )
        )
        log.info(
            "Received response from gemini",
            recommendation=llm_response.data.get("recommendation", "missing"),
            summary=llm_response.data.get("summary", "missing"),
        )
        if not llm_response.success:
            return True, ""
        resolve = True
        if "escalate" in llm_response.data["recommendation"].lower():
            resolve = False
        return resolve, llm_response.data["summary"]

    def handle_side_effect(
        self,
        processing_config: typing.Optional[AlarmProcessingConfig],
        alarm: Alarm,
        new_tap: NUMBER,  # pylint: disable=unused-argument
        new_tags: Tags,  # pylint: disable=unused-argument
        old_tap: typing.Optional[NUMBER],  # pylint: disable=unused-argument
        old_tags: typing.Optional[Tags],  # pylint: disable=unused-argument
        new_state: AlarmState,  # pylint: disable=unused-argument
        ml_service_response: typing.Optional[MLServiceResponse] = None,  # pylint: disable=unused-argument
    ):
        if not self._should_process(
            alarm, new_tap, processing_config, new_tags
        ):
            return
        (
            sop_text,
            source_camera_name,
            checkThreats,
            location_timezone,
            location_name,
        ) = self._setup(alarm)
        assert alarm.source_entity_id is not None
        if self._use_default_llm_alarm_analyzer_config():
            llm_alarm_analyzer_config = LLMAlarmAnalyzerConfig()
            if (
                processing_config is not None
                and processing_config.llmAlarmAnalyzerConfig is not None
            ):
                llm_alarm_analyzer_config = (
                    processing_config.llmAlarmAnalyzerConfig
                )
        else:
            assert processing_config is not None
            assert processing_config.llmAlarmAnalyzerConfig is not None
            llm_alarm_analyzer_config = (
                processing_config.llmAlarmAnalyzerConfig
            )

        use_async = llm_alarm_analyzer_config.async_processing
        analyzeLocationAlarm = llm_alarm_analyzer_config.analyzeLocationAlarm
        resolveRawAlarm = self._should_resolve_raw_alarm(
            llm_alarm_analyzer_config.resolveRawAlarm,
            alarm.tenant_id,
            alarm.source_entity_id,
            len(new_tags.entities),
        )
        escalateRawAlarm = llm_alarm_analyzer_config.escalateRawAlarm
        resolveLocationAlarm = llm_alarm_analyzer_config.resolveLocationAlarm
        sendInlineVideo = llm_alarm_analyzer_config.sendInlineVideo
        sendInlineFrames = llm_alarm_analyzer_config.sendInlineFrames
        slowDownVideoFlag = llm_alarm_analyzer_config.slowDownVideoFlag
        temperature = llm_alarm_analyzer_config.temperature
        update_location_alarm_tap = (
            llm_alarm_analyzer_config.updateLocationAlarmTap
        )
        usePreviousLocationAlarmExplanation = (
            llm_alarm_analyzer_config.usePreviousLocationAlarmExplanation
        )
        if self.side_effect_order == "pre":
            if resolveRawAlarm:
                use_async = False
                analyzeRawAlarm = True
                analyzeLocationAlarm = False
                resolveLocationAlarm = False
                update_location_alarm_tap = False
            else:
                log.info("Use pre side effect only when resolving Raw Alarms")
                return
        elif self.side_effect_order == "post":
            # use_async is same as config since async can be used here
            if resolveRawAlarm:
                # Raw alarm has been processed when "pre" side effect was called
                analyzeRawAlarm = False
                # analyzeLocationAlarm depends on config
                # resolveLocationAlarm depends on config
            else:
                # Raw alarm needs to be processed now
                analyzeRawAlarm = True
                # analyzeLocationAlarm depends on config
                # resolveLocationAlarm depends on config
        else:
            log.error("Invalid side effect order, skipping LLM alarm analyzer")
            return

        previous_analysis, previous_alarms, loc_alarm = self._setup_alarm_data(
            alarm, analyzeLocationAlarm, usePreviousLocationAlarmExplanation
        )
        if (
            loc_alarm is not None
            and loc_alarm.current_status == LocationAlarmStatus.RESOLVED
        ):
            log.info(
                "Location Alarm Status",
                loc_status=loc_alarm.current_status,
            )
            return

        if use_async and alarm.alarm_type == ALARM_TYPE_MOTION:
            task = (
                alarm,
                new_tags,
                loc_alarm,
                resolveRawAlarm,
                escalateRawAlarm,
                resolveLocationAlarm,
                update_location_alarm_tap,
                (
                    alarm.video_start_time_utc,
                    alarm.video_end_time_utc,
                ),
                sop_text,
                source_camera_name,
                location_name,
                location_timezone,
                previous_analysis,
                previous_alarms,
                analyzeRawAlarm,
                analyzeLocationAlarm,
                sendInlineVideo,
                sendInlineFrames,
                checkThreats,
                slowDownVideoFlag,
                temperature,
            )
            try:
                self.task_queue.put(task, block=False)
            except Exception:
                log.warning(
                    "Too many gemini calls in queue, considering increasing max queue size"
                )
        else:
            self.process_alarm(
                alarm,
                new_tags,
                loc_alarm,
                resolveRawAlarm,
                escalateRawAlarm,
                resolveLocationAlarm,
                update_location_alarm_tap,
                (
                    alarm.video_start_time_utc,
                    alarm.video_end_time_utc,
                ),
                sop_text,
                source_camera_name,
                location_name,
                location_timezone,
                previous_analysis,
                previous_alarms,
                analyzeRawAlarm,
                analyzeLocationAlarm,
                sendInlineVideo,
                sendInlineFrames,
                checkThreats,
                slowDownVideoFlag,
                temperature,
            )


class MotionLLMAlarmAnalyzerSideEffect(LLMAlarmAnalyzerSideEffect):
    def _should_process(
        self,
        alarm: Alarm,
        new_tap: NUMBER,
        processing_config: typing.Optional[AlarmProcessingConfig],
        new_tags: Tags,
    ) -> bool:
        if not alarm.display:
            log.info(
                "Alarm is hidden, skipping LLM alarm analyzer side effect"
            )
            return False
        if new_tap < 50:
            return False
        if not alarm.source_entity_id:
            log.warning("Alarm does not have a source entity id")
            return False
        # Disable only if config exists to explicitly disable, default is enabled
        if (
            processing_config is not None
            and processing_config.llmAlarmAnalyzerConfig is not None
            and not processing_config.llmAlarmAnalyzerConfig.enabled
        ):
            log.warning("LLM Alarm Analyzer is disabled, skipping side effect")
            return False
        if alarm.video_path is None:
            log.warning(
                "Alarm does not have video_path set, skipping LLM alarm analyzer"
            )
            return False
        if not alarm.alarm_type == ALARM_TYPE_MOTION:
            return False
        return True

    def _use_default_llm_alarm_analyzer_config(self) -> bool:
        return True

    def _should_resolve_raw_alarm(
        self,
        resolveRawAlarm: bool,
        tenant_id: str,
        source_entity_id: str,
        n_entities: int,
    ) -> bool:
        return resolveRawAlarm

    def _setup(self, alarm: Alarm):
        assert alarm.source_entity_id is not None
        source_camera = self._controller.camera.get_camera_by_id(
            alarm.source_entity_id
        )
        if not source_camera:
            log.error("Camera not found", camera_id=alarm.source_entity_id)
            return

        source_location_id = source_camera.location_id
        source_location = self._controller.locations.get_location_by_id(
            source_location_id
        )
        if not source_location:
            log.error("Location not found", location_id=source_location_id)
            return
        location_name = source_location.name
        location_timezone = source_location.timezone
        sop = self._controller.sop.get_sop(
            tenant_id=alarm.tenant_id, location_id=source_location_id
        )
        if not sop:
            log.error(
                "SOP not found",
                tenant_id=alarm.tenant_id,
                location_id=source_location_id,
            )
            return
        sop_text = self.transform_relevant_sections_of_sop(sop.sop)
        log.debug(
            "Transformed SOP",
            sop_text=sop_text,
            tenant_id=alarm.tenant_id,
            location_id=source_location_id,
        )
        source_camera_name = source_camera.name
        checkThreats = source_location.config.get("checkThreats", False)
        return (
            sop_text,
            source_camera_name,
            checkThreats,
            location_timezone,
            location_name,
        )

    def transform_relevant_sections_of_sop(self, sop: str) -> str:
        """Converts a SOP json to text for the prompt by extracting relevant
        sections. Each relevant section in the SOP dict must be a list of strings
        """
        try:
            sop_dict: typing.Dict[str, typing.Any] = json.loads(sop)

            if "sop_workflow" not in sop_dict or not isinstance(
                sop_dict["sop_workflow"], typing.Dict
            ):
                log.error(
                    "sop_workflow key not found in SOP", sop_dict=sop_dict
                )
                return sop
            sop_dict = sop_dict["sop_workflow"]

            # Relevant sections to be picked from the SOP with descriptions
            descriptions = {
                "emergencySituations": "Emergency Situations (important to escalate)",
                "nonEmergencySituations": "Non-Emergency Situations (still need to be escalated)",
                "exceptions": "Exceptions (situations describing expected behaviour)",
                "notes": "Notes (additional information)",
            }

            relevant_sop_sections: typing.Dict[str, typing.List] = dd(list)
            if "situations" in sop_dict:
                for situation in sop_dict["situations"]:
                    if (
                        isinstance(situation, dict)
                        and "label" in situation
                        and "color" in situation
                    ):
                        if situation["color"] == "red":
                            relevant_sop_sections[
                                "emergencySituations"
                            ].append(situation["label"])
                        elif situation["color"] == "green":
                            relevant_sop_sections[
                                "nonEmergencySituations"
                            ].append(situation["label"])
                        # Ignoring blue labels
            if "exceptions" in sop_dict and isinstance(
                sop_dict["exceptions"], list
            ):
                relevant_sop_sections["exceptions"] = sop_dict["exceptions"]
            if "notes" in sop_dict and isinstance(sop_dict["notes"], list):
                relevant_sop_sections["notes"] = sop_dict["notes"]

            sop_prompt = "\n".join(
                [
                    f"{descriptions.get(k) or k}: \n- " + "\n- ".join(v)
                    for k, v in relevant_sop_sections.items()
                ]
            )
            return sop_prompt
        except Exception as e:
            log.error("Error parsing SOP", error=e)
            return sop

    def _setup_alarm_data(
        self,
        alarm: Alarm,
        analyzeLocationAlarm: bool,
        usePreviousLocationAlarmExplanation: bool = False,
    ):
        previous_analysis: typing.List[typing.Dict] = []
        previous_alarms: typing.List[str] = []
        loc_alarm = None
        if analyzeLocationAlarm and alarm.alarm_type == ALARM_TYPE_MOTION:
            loc_alarms = self._controller.location_alarms.get_location_alarms_for_raw_alarm(
                alarm.alarm_uuid
            )
            if loc_alarms:
                # Populate raw alarms for location alarm
                loc_alarm = (
                    self._controller.location_alarms.get_location_alarm_by_id(
                        loc_alarms[0].int_id
                    )
                )
                assert loc_alarm is not None
                # This will contain analysis of current raw alarm if that was processed in "pre"
                previous_analysis, previous_alarms = (
                    self.get_previous_analysis(
                        loc_alarm, alarm, usePreviousLocationAlarmExplanation
                    )
                )
        return previous_analysis, previous_alarms, loc_alarm

    def process_alarm(
        self,
        alarm: Alarm,
        tags: Tags,
        loc_alarm: typing.Optional[LocationAlarms],
        resolve_raw_alarms: bool,
        escalate_raw_alarms: bool,
        resolve_loc_alarms: bool,
        update_location_alarm_tap: bool,
        video_times: typing.Tuple[datetime, datetime],
        sop: typing.Optional[str],
        camera_name: str,
        location_name: str,
        location_timezone: str,
        previous_analysis: typing.List[typing.Dict[str, str]],
        previous_alarms: typing.List[str],
        analyzeRawAlarm: bool,
        analyzeLocationAlarm: bool,
        send_inline_video: bool,
        send_inline_frames: bool,
        check_threats: bool,
        slow_down_video_flag: bool,
        temperature: float,
    ):
        with tempfile.TemporaryDirectory() as tmp_dir:
            alarm_video_path = download_alarm_video(alarm.video_path, tmp_dir)
            start_ts, end_ts = video_times
            if start_ts is None:
                start_ts = alarm.alarm_time or alarm.created_at_utc
            if end_ts is None:
                end_ts = start_ts + timedelta(seconds=5)
            video_times = (start_ts, end_ts)
            log.info("Analyizng Alarm with Gemini", alarm_id=alarm.alarm_uuid)
            if slow_down_video_flag:
                slow_alarm_video_path = str(
                    Path(alarm_video_path).with_name(
                        Path(alarm_video_path).stem
                        + "_slow"
                        + Path(alarm_video_path).suffix
                    )
                )
                slow_down_video(alarm_video_path, slow_alarm_video_path)
                alarm_video_path = slow_alarm_video_path
            results: LLMAlarmAnalyzerSideEffectResults = (
                LLMAlarmAnalyzerSideEffectResults()
            )
            if check_threats:
                should_resolve_threat, threat_description = (
                    self._llm_threat_classify(
                        alarm.tenant_id, alarm_video_path, tags, False
                    )
                )
                if not should_resolve_threat:
                    self._publish_threat_alarm(alarm, threat_description)
                log.info(
                    "Threat classification done",
                    should_resolve=should_resolve_threat,
                    description=threat_description,
                )

            if analyzeRawAlarm:
                start_time = time.time()
                frames = None
                if send_inline_frames:
                    frames = extract_and_encode_frames(
                        alarm_video_path, video_times, fps=1
                    )
                    log.debug(
                        "Number of frames extracted", num_frames=len(frames)
                    )
                ai_result_ra: AIResultRA = self._analyzer.analyze_alarm(
                    tenant_id=alarm.tenant_id,
                    video_path=alarm_video_path,
                    video_times=video_times,
                    sop=sop,
                    camera_name=camera_name,
                    location_timezone=location_timezone,
                    send_inline_video=send_inline_video,
                    send_inline_frames=send_inline_frames,
                    temperature=temperature,
                    frames=frames,
                )
                time_taken = time.time() - start_time
                try:
                    input_type = "video"
                    if send_inline_video:
                        input_type = "video"
                    elif send_inline_frames:
                        input_type = "frames"
                    self._metrics["rg_llm_raw_alarms_processed"].labels(
                        tenant=alarm.tenant_id,
                        success=str(ai_result_ra.success),
                        reason=ai_result_ra.reason or "none",
                        recommendation=ai_result_ra.recommendation or "none",
                    ).inc()
                    self._metrics[
                        "rg_llm_raw_alarm_processing_seconds"
                    ].labels(
                        tenant=alarm.tenant_id,
                        camera=camera_name,
                        input_type=input_type,
                    ).observe(time_taken)
                    if ai_result_ra.input_tokens:
                        self._metrics["rg_llm_raw_alarm_tokens"].labels(
                            tenant=alarm.tenant_id,
                            camera=camera_name,
                            type="input",
                        ).inc(ai_result_ra.input_tokens)
                    if ai_result_ra.output_tokens:
                        self._metrics["rg_llm_raw_alarm_tokens"].labels(
                            tenant=alarm.tenant_id,
                            camera=camera_name,
                            type="output",
                        ).inc(ai_result_ra.output_tokens)

                except Exception as e:
                    log.warning(
                        "Failed to update RG metrics for raw alarm",
                        alarm_id=alarm.alarm_uuid,
                        error=str(e),
                    )

                results.ra_analysis = ai_result_ra.analysis
                results.ra_recommendation = ai_result_ra.recommendation
                if ai_result_ra.analysis and ai_result_ra.recommendation:
                    vs_str = format_utc_to_local_with_weekday(
                        video_times[0], location_timezone
                    )
                    ve_str = format_utc_to_local_with_weekday(
                        video_times[1], location_timezone
                    )
                    previous_alarms.append(alarm.alarm_uuid)
                    previous_analysis.append(
                        {
                            "text": (
                                f"Camera: {camera_name} "
                                f"Video start time: {vs_str} "
                                f"Video end time: {ve_str} "
                                f"Video explanation: {ai_result_ra.analysis} "
                            )
                        }
                    )
            if analyzeLocationAlarm and previous_analysis and loc_alarm:
                start_time = time.time()
                ai_result_la: AIResultLA = (
                    self._analyzer.analyze_location_alarm(
                        tenant_id=alarm.tenant_id,
                        previous_analysis=previous_analysis,
                        sop=sop,
                        location_timezone=location_timezone,
                        temperature=temperature,
                    )
                )
                time_taken = time.time() - start_time
                results.la_explanation = ai_result_la.explanation
                results.la_summary = ai_result_la.summary
                results.la_recommendation = ai_result_la.recommendation
                results.la_score = ai_result_la.score
                results.la_state = {"raw_alarms_id": previous_alarms}
                try:
                    self._metrics["rg_llm_location_alarms_processed"].labels(
                        tenant=alarm.tenant_id,
                        success=str(ai_result_la.success),
                        reason=ai_result_la.reason or "none",
                        recommendation=ai_result_la.recommendation or "none",
                    ).inc()
                    self._metrics[
                        "rg_llm_location_alarm_processing_seconds"
                    ].labels(
                        tenant=alarm.tenant_id,
                        location=location_name,
                    ).observe(time_taken)
                    if ai_result_la.input_tokens:
                        self._metrics["rg_llm_location_alarm_tokens"].labels(
                            tenant=alarm.tenant_id,
                            type="input",
                            location=location_name,
                        ).inc(ai_result_la.input_tokens)
                    if ai_result_la.output_tokens:
                        self._metrics["rg_llm_location_alarm_tokens"].labels(
                            tenant=alarm.tenant_id,
                            type="output",
                            location=location_name,
                        ).inc(ai_result_la.output_tokens)
                except Exception as e:
                    log.warning(
                        "Failed to update metrics for location alarm",
                        location_alarm_id=loc_alarm.int_id,
                        error=str(e),
                    )
            log.info("Finish gemini request", alarm_id=alarm.alarm_uuid)
            self.postprocess(
                alarm,
                loc_alarm,
                resolve_raw_alarms,
                escalate_raw_alarms,
                resolve_loc_alarms,
                update_location_alarm_tap,
                results,
            )

    def postprocess(
        self,
        alarm: Alarm,
        loc_alarm: typing.Optional[LocationAlarms],
        resolve_raw_alarms: bool,
        escalate_raw_alarms: bool,
        resolve_loc_alarms: bool,
        update_location_alarm_tap: bool,
        result: LLMAlarmAnalyzerSideEffectResults,
    ):
        with ctrl_pool.get() as thread_safe_controller:  # type: ignore
            controller: ctrl.ControllerMap = thread_safe_controller
            log.info("Alarm Analyzer Postprocess", result=result)
            if result.ra_analysis and result.ra_recommendation:
                controller.ai_outputs.add_ai_output_for_raw_alarm(
                    raw_alarm_id=alarm.alarm_uuid,
                    tenant_id=alarm.tenant_id,
                    analysis=result.ra_analysis,
                    recommendation=result.ra_recommendation,
                )
                log.info("Added to AI output table")
                controller.alarm.add_comment(
                    alarm_id=alarm.alarm_uuid,
                    tenant_id=alarm.tenant_id,
                    comment_str=f"Recommendation:{result.ra_recommendation}, Analysis: {result.ra_analysis}",
                )
                log.info("Added comment")
                if (
                    resolve_raw_alarms
                    and result.ra_recommendation.lower() == "resolve"
                ):
                    log.info("Resolving raw alarm")
                    controller.alarm.update_tap(
                        alarm_id=alarm.alarm_uuid,
                        tenant_id=alarm.tenant_id,
                        tap=39,
                        new_ml_outputs=False,
                    )
                    controller.alarm.resolve_alarm(
                        alarm_id=alarm.alarm_uuid, tenant_id=alarm.tenant_id
                    )
                if (
                    escalate_raw_alarms
                    and result.ra_recommendation.lower() == "escalate"
                ):
                    log.info("Escalating raw alarm")
                    # Increase TAP to 95
                    controller.alarm.update_tap(
                        alarm_id=alarm.alarm_uuid,
                        tenant_id=alarm.tenant_id,
                        tap=95,
                        new_ml_outputs=False,
                    )

            if (
                result.la_summary
                and result.la_recommendation
                and result.la_state
                and result.la_explanation
                and loc_alarm
            ):
                controller.ai_outputs.add_ai_output_for_location_alarm(
                    location_alarm_id=loc_alarm.int_id,
                    tenant_id=alarm.tenant_id,
                    summary=result.la_summary,
                    recommendation=result.la_recommendation,
                    raw_state=result.la_state,
                    explanation=result.la_explanation,
                )
                location_alarm_update = LocationAlarmUpdates(
                    location_alarm_id=loc_alarm.int_id,
                    update_type=LocationAlarmUpdateType.ADD_AI_COMMENT,
                    update_text=f"Recommendation:{result.la_recommendation}  Summary:{result.la_summary}",
                    user_id=config.HAIE.HAKIMO_USER_ID,
                )
                controller.location_alarms.update_location_alarm(
                    location_alarm_update=location_alarm_update,
                    location_alarm=loc_alarm,
                )
                if (
                    resolve_loc_alarms
                    and result.la_recommendation.lower() == "resolve"
                ):
                    controller.location_alarms.update_location_alarm_status(
                        location_alarm=loc_alarm,
                        new_status=LocationAlarmStatus.RESOLVED,
                        update_text="Resolved by AI Operator",
                        user_id=config.HAIE.HAKIMO_USER_ID,
                    )
                if (
                    update_location_alarm_tap
                    and result.la_score
                    and result.la_score >= 50
                    and loc_alarm.tap != result.la_score
                ):
                    controller.location_alarms.update_location_alarm_status(
                        location_alarm=loc_alarm,
                        tap=result.la_score,
                        update_text=f"Location Alarm TAP Updated by AI Operator to {result.la_score}",
                        user_id=config.HAIE.HAKIMO_USER_ID,
                    )

    def get_previous_analysis(
        self,
        loc_alarm: LocationAlarms,
        current_alarm: Alarm,
        usePreviousLocationAlarmExplanation: bool = False,
    ) -> typing.Tuple[typing.List[typing.Dict], typing.List[str]]:
        previous_analysis: typing.List[typing.Dict] = []
        previous_alarms: typing.List[str] = []
        if not isinstance(loc_alarm.raw_alarms, list):
            return previous_analysis, previous_alarms
        if usePreviousLocationAlarmExplanation:
            la_ai_output = self._controller.ai_outputs.get_latest_ai_output_for_location_alarm(
                loc_alarm.int_id
            )
            if la_ai_output is not None:
                raw_state = la_ai_output.raw_state
                latest_ai_explanation = (
                    la_ai_output.explanation
                    if la_ai_output.explanation
                    else la_ai_output.summary
                )
                if raw_state and latest_ai_explanation:
                    # latest explanation of location alarm
                    previous_analysis.append(
                        {
                            "text": (
                                "Here is a summary of the recent activity at this site, which includes notable incidents and observations detected in earlier video events. "
                                f"summary: {latest_ai_explanation}\n"
                                "Here is the analysis from new video events from this site:"
                            )
                        }
                    )
                    for ra in loc_alarm.raw_alarms:
                        if ra.uuid in raw_state["raw_alarms_id"]:
                            previous_alarms.append(ra.uuid)
                            continue
                        ra_subprompt = self.compose_subprompt_from_raw_alarm(
                            ra
                        )
                        if ra_subprompt is None:
                            continue
                        previous_alarms.append(ra.uuid)
                        previous_analysis.append(ra_subprompt)
                    return previous_analysis, previous_alarms
        for ra in loc_alarm.raw_alarms:
            ra_subprompt = self.compose_subprompt_from_raw_alarm(ra)
            if ra_subprompt is None:
                continue
            previous_alarms.append(ra.uuid)
            previous_analysis.append(ra_subprompt)
        return previous_analysis, previous_alarms

    def compose_subprompt_from_raw_alarm(self, raw_alarm: Alarm):
        assert isinstance(raw_alarm, RawAlarms)
        ra_cam = self._controller.camera.get_camera_by_id(
            raw_alarm.source_entity_id, include_location=True
        )
        assert ra_cam is not None
        ra_cam_name = ra_cam.name
        ra_ai_output = (
            self._controller.ai_outputs.get_latest_ai_output_for_raw_alarm(
                raw_alarm.uuid
            )
        )
        if ra_ai_output is None:
            return None

        ra_ai_analysis = ra_ai_output.analysis
        tz_name = ra_cam.location.timezone
        ra_vs_str = format_utc_to_local_with_weekday(
            raw_alarm.video_start_timestamp_utc, tz_name
        )
        ra_ve_str = format_utc_to_local_with_weekday(
            raw_alarm.video_end_timestamp_utc, tz_name
        )

        ra_subprompt = {
            "text": (
                f"Camera: {ra_cam_name} "
                f"Video start time: {ra_vs_str} "
                f"Video end time: {ra_ve_str} "
                f"Video explanation: {ra_ai_analysis} "
            )
        }
        return ra_subprompt


class EnterpriseLLMAlarmAnalyzerSideEffect(LLMAlarmAnalyzerSideEffect):
    def __init__(
        self,
        controller: ctrl.ControllerMap,
        side_effect_order: typing.Optional[str] = None,
    ):
        side_effect_order = "post"
        super().__init__(controller, side_effect_order)
        self._event_handler = AlarmEventHandler(controller, self._rds_client)

    def _should_process(
        self,
        alarm: Alarm,
        new_tap: NUMBER,
        processing_config: typing.Optional[AlarmProcessingConfig],
        new_tags: Tags,
    ) -> bool:
        if not alarm.display:
            log.info(
                "Alarm is hidden, skipping LLM alarm analyzer side effect"
            )
            return False
        if new_tap < 50:
            return False
        if not alarm.source_entity_id:
            log.warning("Alarm does not have a source entity id")
            return False
        # Enable only if config exists to explicitly enable, default is disabled
        if (
            processing_config is None
            or not processing_config.llmAlarmAnalyzerConfig
            or not processing_config.llmAlarmAnalyzerConfig.enabled
        ):
            log.warning("LLM Alarm Analyzer is disabled, skipping side effect")
            return False
        if alarm.video_path is None:
            log.warning(
                "Alarm does not have video_path set, skipping LLM alarm analyzer"
            )
            return False
        if alarm.alarm_type == ALARM_TYPE_MOTION:
            return False
        elif (
            TagTypes.UNAUTHORIZED_ENTRY.name not in new_tags.video_tag_names
        ) and (TagTypes.ENTRY.name not in new_tags.video_tag_names):
            return False
        return True

    def _use_default_llm_alarm_analyzer_config(self) -> bool:
        return False

    def _should_resolve_raw_alarm(
        self,
        resolveRawAlarm: bool,
        tenant_id: str,
        source_entity_id: str,
        n_entities: int,
    ) -> bool:
        max_entities = cloud_config_manager.is_enabled(
            "tailgating_llm_autoresolve.max_entities"
        )
        if isinstance(max_entities, int):
            if max_entities < n_entities:
                return False
        enabled_doors = cloud_config_manager.is_enabled(
            "tailgating_llm_autoresolve.enabled_doors"
        )
        if isinstance(enabled_doors, list):
            if source_entity_id in enabled_doors:
                return True
        enabled_tenants = cloud_config_manager.is_enabled(
            "tailgating_llm_autoresolve.enabled_tenants"
        )
        if isinstance(enabled_tenants, list):
            if tenant_id in enabled_tenants:
                return True
        if resolveRawAlarm:
            return True
        return False

    def _llm_enterprise_classify(
        self, alarm: Alarm, alarm_video_path: str, tags: Tags
    ) -> typing.Tuple[bool, str]:
        ag_times = self._event_handler.get_nearby_alarm_times(alarm)
        event_times = AlarmEventHandler.get_event_times(
            alarm, alarm_video_path, tags
        )
        only_event_times = [x[0] for x in event_times]
        is_split, split_times = self._event_handler.get_split_times(
            ag_times, only_event_times, alarm
        )
        responses = []
        for st_split, en_split in split_times:
            if is_split:
                alarm_video_path = (
                    self._event_handler._split_video_into_segments(
                        alarm_video_path,
                        (
                            st_split - alarm.video_start_time_utc
                        ).total_seconds(),
                        (
                            en_split - alarm.video_start_time_utc
                        ).total_seconds(),
                    )
                )
            annotated_alarm_video_path = self._event_handler.annotate_video(
                alarm, alarm_video_path
            )
            text = self._event_handler._construct_enterprise_text(
                [t for t in ag_times if (t < en_split and t > st_split)],
                st_split,
                [
                    t
                    for t in only_event_times
                    if (t < en_split and t > st_split)
                ],
            )
            llm_response: LLMRequestResponse = (
                self._analyzer.send_request_with_video(
                    tenant_id=alarm.tenant_id,
                    text=text,
                    video_path=annotated_alarm_video_path,
                    use_video_uri=False,
                )
            )
            if llm_response.success:
                responses.append(llm_response.data)
        should_resolve = True
        descriptions = []
        for resp in responses:
            if "escalate" in resp["recommendation"].lower():
                should_resolve = False
            descriptions.append(resp["description"])
        return should_resolve, ". ".join(descriptions)

    def _setup_alarm_data(
        self,
        alarm: Alarm,
        analyzeLocationAlarm: bool,
        usePreviousLocationAlarmExplanation: bool = False,
    ):
        return [], [], None

    def _setup(self, alarm: Alarm):
        source_door = self._controller.door.get_door(alarm.source_entity_id)
        assert source_door is not None
        source_location_id = source_door.location_id
        source_location = self._controller.locations.get_location_by_id(
            source_location_id
        )
        if not source_location:
            log.error("Location not found", location_id=source_location_id)
            return
        location_timezone = source_location.timezone
        checkThreats = source_location.config.get("checkThreats", False)
        sop_text = ""
        source_camera_name = ""
        location_name = source_location.name
        return (
            sop_text,
            source_camera_name,
            checkThreats,
            location_timezone,
            location_name,
        )

    def process_alarm(
        self,
        alarm: Alarm,
        tags: Tags,
        loc_alarm: typing.Optional[LocationAlarms],
        resolve_raw_alarms: bool,
        escalate_raw_alarms: bool,
        resolve_loc_alarms: bool,
        update_location_alarm_tap: bool,
        video_times: typing.Tuple[datetime, datetime],
        sop: typing.Optional[str],
        camera_name: str,
        location_name: str,
        location_timezone: str,
        previous_analysis: typing.List[typing.Dict[str, str]],
        previous_alarms: typing.List[str],
        analyzeRawAlarm: bool,
        analyzeLocationAlarm: bool,
        send_inline_video: bool,
        send_inline_frames: bool,
        check_threats: bool,
        slow_down_video_flag: bool,
        temperature: float,
    ):
        threat_description, classify_description = None, None
        should_resolve, should_resolve_classify = False, False

        with tempfile.TemporaryDirectory() as tmp_dir:
            alarm_video_path = download_alarm_video(alarm.video_path, tmp_dir)
            if check_threats:
                should_resolve_threat, threat_description = (
                    self._llm_threat_classify(
                        alarm.tenant_id, alarm_video_path, tags, True
                    )
                )
                should_resolve = should_resolve or should_resolve_threat
                if not should_resolve_threat:
                    self._publish_threat_alarm(alarm, threat_description)
                log.info(
                    "Threat classification done",
                    should_resolve=should_resolve_threat,
                    description=threat_description,
                )
            if TagTypes.UNAUTHORIZED_ENTRY.name in tags.video_tag_names:
                should_resolve_classify, classify_description = (
                    self._llm_enterprise_classify(
                        alarm, alarm_video_path, tags
                    )
                )
                should_resolve = should_resolve or should_resolve_classify
                log.info(
                    "Unauthorized entry classification done",
                    should_resolve=should_resolve_classify,
                    description=classify_description,
                )
        if classify_description is not None:
            with ctrl_pool.get() as controller:  # type: ignore
                if should_resolve_classify and resolve_raw_alarms:
                    assert alarm.source_id is not None
                    tailgating_source_id = "derived/" + alarm.source_id
                    tailgating_uuid = controller.alarm.alarm_id_by_source_id(
                        tailgating_source_id, alarm.tenant_id
                    )
                    controller.alarm.resolve_alarm(
                        alarm_id=tailgating_uuid, tenant_id=alarm.tenant_id
                    )
                    controller.alarm.update_tap(
                        tailgating_uuid,
                        alarm.tenant_id,
                        37,
                    )
                controller.ai_outputs.add_ai_output_for_raw_alarm(
                    raw_alarm_id=alarm.alarm_uuid,
                    tenant_id=alarm.tenant_id,
                    analysis=classify_description,
                    recommendation="Resolve" if should_resolve else "Escalate",
                )
                controller.alarm.add_comment(
                    alarm_id=alarm.alarm_uuid,
                    tenant_id=alarm.tenant_id,
                    comment_str=classify_description,
                )


class AlarmEventHandler:
    def __init__(self, controller: ctrl.ControllerMap, rds_client: RDSClient):
        self._controller = controller
        self._rds_client = rds_client

    def get_split_times(
        self,
        ag_times: typing.List[datetime],
        event_times: typing.List[datetime],
        alarm: Alarm,
    ):
        if (
            alarm.video_start_time_utc - alarm.video_end_time_utc
        ).total_seconds() < 60:
            return False, [
                (
                    alarm.video_start_time_utc,
                    alarm.video_end_time_utc,
                )
            ]
        all_times = sorted(ag_times + event_times)
        return True, [
            (
                alarm.video_start_time_utc + timedelta(seconds=st),
                alarm.video_start_time_utc + timedelta(seconds=en),
            )
            for (st, en) in segment_range(
                (
                    alarm.video_end_time_utc - alarm.video_start_time_utc
                ).total_seconds(),
                [
                    (a - alarm.video_start_time_utc).total_seconds()
                    for a in all_times
                ],
                min_segment_size=20,
                max_segment_size=30,
            )
        ]

    @staticmethod
    def get_cropped_box(tags: Tags) -> typing.Tuple[int, int, int, int, bool]:
        x1, y1, x2, y2 = int(1e6), int(1e6), 0, 0
        if len(tags.entities) == 0:
            return x1, y1, x2, y2, False
        for entity in tags.entities:
            if isinstance(entity, Entity):
                entity_dict = entity.to_json()
            else:
                entity_dict = entity
            track = entity_dict["track"]
            assert isinstance(track, dict)
            for box in track["boxes"]:
                x, y, w, h = box["box"]
                x1 = int(min(x1, x))
                y1 = int(min(y1, y))
                x2 = int(max(x2, x + w))
                y2 = int(max(y2, y + h))
        return (max(x1 - 10, 0), max(y1 - 10, 0), x2, y2, True)

    @staticmethod
    def get_people_start_end(
        tags: Tags, fps: typing.Union[float, int]
    ) -> typing.Tuple[str, str]:
        f1, f2 = int(1e6), 0
        for entity in tags.entities:
            if isinstance(entity, Entity):
                entity_dict = entity.to_json()
            else:
                entity_dict = entity
            track = entity_dict["track"]
            frames = track["frames"]
            f1 = min(min(frames), f1)
            f2 = max(max(frames), f2)
        return timedelta_to_str(timedelta(seconds=f1 / fps)), timedelta_to_str(
            timedelta(seconds=f2 / fps)
        )

    @staticmethod
    def get_event_times(
        alarm: Alarm, video_path: str, tags: Tags
    ) -> typing.List[typing.Tuple[datetime, str, str]]:
        video_start_time = alarm.video_start_time_utc
        event_times = []
        fps = get_video_fps(video_path)
        for entity in tags.entities:
            if isinstance(entity, Entity):
                entity_dict = entity.to_json()
            else:
                entity_dict = entity
            for transition in entity_dict["transitions"]:
                event_times.append(
                    (
                        video_start_time
                        + timedelta(seconds=transition["idx"] / fps),
                        transition["transition_type"],
                        transition["transition_cause"],
                    )
                )
        return sorted(event_times)

    @staticmethod
    def format_words(words):
        if not words:
            return ""
        if len(words) == 1:
            return words[0]
        if len(words) == 2:
            return f"{words[0]} and {words[1]}"
        return f"{', '.join(words[:-1])} and {words[-1]}"

    def get_nearby_alarm_times(self, alarm: Alarm) -> typing.List[datetime]:
        acc_granted_type = self._rds_client.get_alarm_type_id_from_alarm_type(
            AlarmMapper.get_ag_regex() + r"|(Open Door Command Issued.*)",
            regex=True,
        )
        assert alarm.door_uuid is not None
        filters = AlarmFilters(
            utc_time_interval=(
                alarm.video_start_time_utc,
                alarm.video_end_time_utc,
            ),
            display_only=False,
            door_uuid=([alarm.door_uuid], True),
            alarm_type_uuid=(acc_granted_type, True),
            order_desc=False,
        )
        db_rows = self._controller.alarm.fetch_alarms(
            tenant_ids=[alarm.tenant_id], filters=filters, limit=None
        )
        db_rows = deduplicate_list_with_employee_ids(
            db_rows, employee_ids=[row[0].employee_id for row in db_rows]
        )
        return [row[0].alarm_timestamp_utc for row in db_rows]

    def _split_video_into_segments(
        self, video_path: str, start_time: float, end_time: float
    ) -> str:
        # use ffmpeg to split the video into new video from start_time to end_time
        output_path = video_path.replace(".mp4", "_split.mp4")
        command = [
            "ffmpeg",
            "-i",
            video_path,
            "-ss",
            str(start_time),
            "-to",
            str(end_time),
            "-c:v",
            "copy",
            "-c:a",
            "copy",
            output_path,
        ]
        subprocess.run(command, check=True)
        return output_path

    @staticmethod
    def crop_video(video_path: str, tags: Tags) -> str:
        x1, y1, x2, y2, do_crop = AlarmEventHandler.get_cropped_box(tags)
        if not do_crop:
            return video_path
        fps = get_video_fps(video_path)
        t1, t2 = AlarmEventHandler.get_people_start_end(tags, fps)
        save_path = "crop_temp.mp4"
        crop_w = x2 - x1
        crop_h = y2 - y1
        command = [
            "ffmpeg",
            "-ss",
            t1,
            "-to",
            t2,
            "-i",
            video_path,
            "-filter:v",
            f"crop={crop_w}:{crop_h}:{x1}:{y1}",
            "-r",
            "1",
            "-c:v",
            "libx264",
            "-preset",
            "ultrafast",
            "-crf",
            "28",
            save_path,
            "-y",
        ]
        subprocess.run(command, check=True)
        return save_path

    def annotate_video(self, alarm: Alarm, video_path: str) -> str:
        vid_cap = cv2.VideoCapture(video_path)
        # annotated_video_path = video_path.replace(".mp4", "_annotated.mp4")
        annotated_video_path = "temp.mp4"
        fps = int(vid_cap.get(cv2.CAP_PROP_FPS))
        width = int(vid_cap.get(cv2.CAP_PROP_FRAME_WIDTH))
        height = int(vid_cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
        ret, frame = vid_cap.read()
        fourcc = cv2.VideoWriter_fourcc(*"avc1")
        outfile = cv2.VideoWriter(
            annotated_video_path,
            fourcc,
            1,
            (width, height),
        )
        frame_idx = 0
        while True:
            ret, frame = vid_cap.read()
            if not ret:
                break
            assert alarm.scene_info is not None
            assert alarm.scene_info.door_bbox is not None
            if frame_idx % fps == 0:
                draw_doors(
                    frame,
                    alarm.scene_info.door_bbox,
                    cam_position=alarm.scene_info.camera_position,
                    door_orientation_point=alarm.scene_info.door_orientation_point,
                    alpha=0.2,
                )
                outfile.write(frame)
            frame_idx += 1
        return annotated_video_path

    def _construct_enterprise_text(
        self,
        ag_times: typing.List[datetime],
        video_start_time: datetime,
        event_times: typing.List[datetime],
    ) -> str:
        ag_times_str = self.format_words(
            [timedelta_to_str(tt - video_start_time) for tt in ag_times]
        )
        event_times_str = self.format_words(
            [timedelta_to_str(tt - video_start_time) for tt in event_times]
        )
        return (
            "You are given a video of an alarm created at a site monitored by a remote security operator. "
            "The door of interest is marked in dark blue with a red line at the bottom. "
            "At the bottom of the door is an arrow pointing in the direction of entry. "
            "You are to describe in detail people entering and exiting through the door of interest along with the timestamp of each entry or exit. "
            "Ignore people passing through other doors or walking nearby. "
            f"The access control system has granted access to the door at the following times: {ag_times_str}. "
            f"There are sensors near the door which detected possible entry or exit at the following times: {event_times_str}. "
            "If there are any people tailgating or piggybacking when another person swipes the card and enters, escalate the alarm. "
            "Two or people entering after a single access granted is considered tailgating and needs to be escalated. "
            "Respond in json format with keys 'description' and 'recommendation' where recommendation is either Resolve or Escalate. "
        )


def segment_range(b, events, min_segment_size=20, max_segment_size=30):
    events = sorted(list(set(events)))
    if not events:
        return [
            (i, min(i + max_segment_size, b))
            for i in range(0, b, max_segment_size)
        ]
    distances = np.zeros(b + 1)
    for i in range(b + 1):
        distances[i] = min(abs(i - event) for event in events)
    segments = []
    current_pos = 0
    while current_pos < b:
        best_end = None
        best_score = -1
        for end_pos in range(
            current_pos + min_segment_size,
            min(current_pos + max_segment_size + 1, b + 1),
        ):
            score = distances[end_pos]
            if score > best_score:
                best_score = score
                best_end = end_pos
        if best_end is None:
            best_end = min(current_pos + max_segment_size, b)
        segments.append((current_pos, best_end))
        current_pos = best_end
    return segments


def visualize_segments(b, events, segments):
    print(f"Range: 0 to {b}")
    print(f"Events: {events}")
    print("\nSegments:")
    for i, (start, end) in enumerate(segments):
        print(f"Segment {i + 1}: [{start}, {end}] (size: {end - start})")
    print("\nVisual representation:")
    scale = 100  # Scale for visualization
    line_length = min(b, scale)
    visual = ["_"] * (line_length + 1)
    for event in events:
        pos = int(event * line_length / b)
        visual[pos] = "E"
    for start, end in segments:
        start_pos = int(start * line_length / b)
        end_pos = int(end * line_length / b)
        if start_pos < len(visual):
            visual[start_pos] = "["
        if end_pos < len(visual):
            visual[end_pos] = "]"
    print("".join(visual))


def run_visualise_segments():
    b = 100
    events = [10, 25, 42, 60, 78, 95]

    segments = segment_range(b, events)
    visualize_segments(b, events, segments)


def print_stats(pred_matches: typing.List[typing.Dict], n_entities: int = -1):
    tp = 0
    fp = 0
    tn = 0
    fn = 0
    select_count = 0
    for pred in pred_matches:
        if n_entities > 1:
            if pred["n_entities"] > n_entities:
                continue
        select_count += 1
        if pred["is_tg"]:
            if pred["tg_pred"]:
                tp += 1
            else:
                fn += 1
        else:
            if not pred["tg_pred"]:
                tn += 1
            else:
                fp += 1
    accuracy = (tp + tn) / (select_count + 1e-9)
    precision = tp / (tp + fp) if tp + fp > 0 else 0
    recall = tp / (tp + fn) if tp + fn > 0 else 0
    f1 = (
        2 * precision * recall / (precision + recall)
        if precision + recall > 0
        else 0
    )
    print("=" * 10)
    print(f"n Entities : {n_entities}")
    print(f"Accuracy: {accuracy}")
    print(f"Precision: {precision}")
    print(f"Recall: {recall}")
    print(f"F1: {f1}")
    print(f"Ignored alarms : {len(pred_matches) - select_count}")
    print(f"Total alarms : {len(pred_matches)}")
    print("=" * 10 + "\n")


def eval_enterprise():
    import csv
    import glob
    import json
    import os
    import sys

    from tqdm import tqdm

    db = RDSClient()
    cm = ctrl.ControllerMap(db.db_adapter)
    se = EnterpriseLLMAlarmAnalyzerSideEffect(cm)

    csv_path = sys.argv[1]
    alarms_dir = sys.argv[2]
    if len(sys.argv) > 3:
        experiment_name = sys.argv[3]
    else:
        experiment_name = "llm_alarm_analyzer"

    pred_matches = []
    with open(csv_path, "r") as f:
        reader = csv.DictReader(f)
        for row in tqdm(list(reader)):
            alarm_id = row["Alarm ID"]
            alm = cm.alarm.fetch_alarms(
                None,
                filters=AlarmFilters(
                    alarm_uuids=[alarm_id],
                    tenant=None,
                ),
                limit=None,
            )[0]
            is_tg = row["Is TG"] == "TRUE"
            assert alm is not None
            alarm_id_dir = os.path.join(alarms_dir, alarm_id)
            if not os.path.isdir(alarm_id_dir):
                continue
            tags_file = list(
                glob.glob(os.path.join(alarm_id_dir, "*tags.json"))
            )
            if not tags_file:
                continue
            with open(tags_file[0], "r") as f:
                tags_json = json.load(f)
            tags_json["tags"] = ["ENTRY", "UNAUTHORIZED_ENTRY"]
            tags = Tags.from_json(tags_json)
            alarm = db.get_alarm_object_from_raw_alarm(alm[0])
            if alarm is None or alarm.scene_info is None:
                log.info(
                    "Skipping alarm as alarm is None or scene_info is None",
                    alarm_id=alarm_id,
                )
                continue
            with tempfile.TemporaryDirectory() as tmp_dir:
                alarm_video_path = download_alarm_video(
                    alarm.video_path, tmp_dir
                )
                not_tg, response = se._llm_enterprise_classify(
                    alarm, alarm_video_path, tags
                )
            data = {
                "alarm_id": alarm_id,
                "is_tg": is_tg,
                "response": response,
                "tg_pred": not not_tg,
                "n_entities": len(tags.entities),
            }
            log.info("Alarm classified", **data)
            pred_matches.append(data)
    with open(f"predictions_{experiment_name}.json", "w") as f:
        json.dump(pred_matches, f, indent=4)
    # calculate accuracy, f1, precision, recall
    for i in range(3, 9):
        print_stats(pred_matches, i)


if __name__ == "__main__":
    # db = RDSClient()
    # cm = ctrl.ControllerMap(db.db_adapter)

    # se = LLMAlarmAnalyzerSideEffect(cm)
    # # alarm = db.get_alarm_object(
    # #     "2d9d08d3-67fa-4483-bcfc-74719af5c57b", "curio-storage"
    # # )
    # # assert alarm is not None
    # # se.handle_side_effect(
    # #     AlarmProcessingConfig(llmAlarmAnalyzerConfig={"enabled": True}),
    # #     alarm,
    # #     90,
    # #     Tags([], {}),
    # #     90,
    # #     Tags([], {}),
    # #     AlarmState.PROCESSED,
    # # )

    # la = cm.location_alarms.get_location_alarm_by_id(1251612)
    # assert la is not None
    # for ra in la.raw_alarms:
    #     ra_obj = db.get_alarm_object_from_raw_alarm(ra)
    #     se.handle_side_effect(
    #         AlarmProcessingConfig(
    #             llmAlarmAnalyzerConfig={
    #                 "enabled": True,
    #                 "analyzeLocationAlarm": True,
    #             }
    #         ),
    #         ra_obj,
    #         90,
    #         Tags([], {}),
    #         90,
    #         Tags([], {}),
    #         AlarmState.PROCESSED,
    #     )

    eval_enterprise()

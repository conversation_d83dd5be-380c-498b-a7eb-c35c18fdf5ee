import time
import typing

import boto3
import face_recognition
import structlog

from common_utils.image_utils import encode_image_from_array
from common_utils.io_helpers import read_file
from config import backend_config as config
from interfaces.entities.entity import Entity
from interfaces.entities.person import Person

log = structlog.get_logger("hakimo", module="AWS Face Rekognition")


class AWSRekognitionFaceRecognizer:
    def __init__(
        self,
        access_key: typing.Optional[str] = None,
        secret_key: typing.Optional[str] = None,
        collection_id: typing.Optional[str] = None,
    ):
        log.debug("Initializing AWSRekognitionFaceRecognizer...")
        if access_key is None or secret_key is None:
            log.debug("Reading AWS credentials from config..")
            access_key = read_file(config.HAIE.AWS_ACCESS_KEY_ID, missing="")
            secret_key = read_file(config.HAIE.AWS_SECRET_KEY, missing="")
        self.collection_id = collection_id or config.HAIE.FACE_RECOGNITION.get(
            "rekognition_collection", ""
        )
        if not self.collection_id:
            log.error(
                "No Rekognition collection_id provided or found in config."
            )

        region = getattr(config.HAIE, "AWS_S3_REGION", "us-west-2")

        self.rekognition_client = boto3.client(
            "rekognition",
            aws_access_key_id=access_key,
            aws_secret_access_key=secret_key,
            region_name=region,
        )
        self.ensure_collection_exists()
        log.debug(
            "AWS Rekognition client initialized",
            collection_id=self.collection_id,
        )

    def ensure_collection_exists(self) -> None:
        """Check if Rekognition collection exists, and create it if not."""
        try:
            self.rekognition_client.describe_collection(
                CollectionId=self.collection_id
            )
            log.debug("Collection exists", collection_id=self.collection_id)
        except self.rekognition_client.exceptions.ResourceNotFoundException:
            log.info(
                "Collection not found. Creating new collection...",
                collection_id=self.collection_id,
            )
            try:
                self.rekognition_client.create_collection(
                    CollectionId=self.collection_id
                )
                log.info(
                    "Collection created successfully",
                    collection_id=self.collection_id,
                )
            except Exception as e:
                log.error(
                    "Failed to create Rekognition collection", error=str(e)
                )

    def recognize_faces(
        self,
        entities: typing.Sequence[Entity],
        similarity_score: float = 70.0,
        biggest_n_boxes: int = 5,
    ) -> typing.List[
        typing.Tuple[str, str, int, typing.Optional[typing.List[float]], float]
    ]:
        log.debug(
            "Starting face recognition process", total_entities=len(entities)
        )
        start_time = time.time()
        matched_entities = []

        for person in entities:
            if not isinstance(person, Person):
                log.debug(
                    "Skipping non-Person entity", entity_type=type(person)
                )
                continue
            if not (person.track and person.track.patches):
                log.debug(
                    "Skipping person with no track or patches",
                    person_id=person.person_id,
                )
                continue

            areas = [
                (patch, frame, patch.shape[0] * patch.shape[1])
                for patch, frame in zip(
                    person.track.patches, person.track.patch_frames
                )
            ]
            biggest_n_patches = sorted(
                areas, key=lambda x: x[2], reverse=True
            )[:biggest_n_boxes]
            for patch, frame, _ in biggest_n_patches:
                try:
                    face_location = face_recognition.face_locations(
                        patch, model="cnn"
                    )
                except Exception as e:
                    log.warning(
                        "face_recognition failed", error=str(e), frame=frame
                    )
                    continue

                if not face_location:
                    log.debug("No face found in patch", frame=frame)
                    continue

                try:
                    encoded_image = encode_image_from_array(
                        patch, return_byte_arr=True
                    )
                except Exception as e:
                    log.warning(
                        "Failed to encode image", error=str(e), frame=frame
                    )
                    continue

                try:
                    response = self.rekognition_client.search_faces_by_image(
                        CollectionId=self.collection_id,
                        Image={"Bytes": encoded_image},
                        FaceMatchThreshold=similarity_score,
                    )
                except Exception as e:
                    log.error(
                        "AWS Rekognition search_faces_by_image failed",
                        error=str(e),
                        frame=frame,
                    )
                    continue

                face_matches = response.get("FaceMatches", [])
                if not face_matches:
                    log.debug(
                        "No face matches found in Rekognition response",
                        frame=frame,
                    )
                    continue

                for match in face_matches:
                    face = match["Face"]
                    similarity = match["Similarity"]
                    matched_profile_id = face["ExternalImageId"]
                    if similarity >= similarity_score:
                        try:
                            det = person.track.boxes[
                                person.track.frames.index(frame)
                            ]
                            box = [float(i) for i in det._box]
                        except ValueError:
                            log.debug(
                                "Frame not found in track.frames for box lookup",
                                frame=frame,
                            )
                            box = person.get_box_around_track()
                        except Exception as e:
                            log.warning(
                                "Error while retrieving bounding box",
                                error=str(e),
                            )
                            box = None

                        log.info(
                            "Match found",
                            person_id=person.person_id,
                            matched_profile_id=matched_profile_id,
                            similarity=similarity,
                            frame=frame,
                            box=box,
                        )
                        matched_entities.append(
                            (
                                person.person_id,
                                matched_profile_id,
                                frame,
                                box,
                                similarity,
                            )
                        )
                        break  # break after first match
        duration = time.time() - start_time
        log.debug(
            "Face recognition process completed",
            total_entities=len(entities),
            matched_entities=len(matched_entities),
            time_taken=duration,
        )
        return matched_entities

import time
from http import H<PERSON>PStatus

import redis
import structlog
from flask import jsonify, request

from common_utils_v1.vision_metrics_definitions import (
    VISION_ALARM_GROUPS_RETRIEVED_TOTAL,
)
from db_controller import DBControllerV1
from models_rds.alarm_group import AlarmGroup
from vision.services.src.event_processor.endpoints.decorators import (
    track_metrics,
)
from vision.services.src.event_processor.handlers.event_types import (
    add_event_redis,
)
from vision.services.src.event_processor.models.events import (
    EventState,
    Resolution,
    Severity,
)

logger = structlog.get_logger(
    "hakimo", module="vision_http_server_vision_command_internal_endpoint"
)


class VisionCommandInternalEndpoint:
    def __init__(self, controller: DBControllerV1, redis_client: redis.Redis):
        self._ctrl_map = controller
        self._redis_client = redis_client

    @track_metrics("add_pending_alarms_to_set")
    def add_pending_alarms_to_set(self):
        """Add pending alarms to a set"""
        try:
            data = request.get_json()
            tenant_ids = data["tenant_ids"]
            limit = data.get("limit", 1)
            order = data.get("order", "asc")
            pipeline = self._redis_client.pipeline()
            alarm_group_controller = self._ctrl_map.alarm_group
            camera_group_ids_with_start_time = (
                alarm_group_controller.get_pending_alarm_groups_for_tenants(
                    tenant_ids, limit, order
                )
            )
            for (
                camera_group_id,
                start_time,
            ) in camera_group_ids_with_start_time:
                # Convert datetime to timestamp (float) for Redis score
                timestamp_score = (
                    start_time.timestamp() if start_time else time.time()
                )
                pipeline.zadd(
                    "pending_camera_groups_ordered",
                    {camera_group_id: timestamp_score},
                )
                # Track alarm groups added to set
                VISION_ALARM_GROUPS_RETRIEVED_TOTAL.labels(
                    camera_group_id=camera_group_id
                ).inc()
            pipeline.execute()
            return jsonify(
                {"status": "ok", "alarm_set": "pending_camera_groups_ordered"}
            )
        except Exception as e:
            logger.error("Error adding pending alarms to set", error=str(e))
            return (
                jsonify({"error": "Internal server error", "details": str(e)}),
                HTTPStatus.INTERNAL_SERVER_ERROR,
            )

    def create_alarm_group(self, tenant_id, camera_group_id, severity):
        """Create an alarm group with the provided data"""
        try:
            data = request.get_json()

            # Validate required fields
            required_fields = ["timestamp_utc"]
            for field in required_fields:
                if field not in data:
                    return (
                        jsonify({"error": f"Missing required field: {field}"}),
                        HTTPStatus.BAD_REQUEST,
                    )

            key = f"alarm_group:{camera_group_id}:{tenant_id}:{severity}"

            # Fetch the alarm_group_id from Redis
            alarm_group_id = self._redis_client.hget(key, "alarm_group_id")

            logger.info(
                "Received create alarm group request",
                tenant_id=tenant_id,
                severity=severity,
                timestamp_utc=data["timestamp_utc"],
                camera_group_id=camera_group_id,
            )

            if alarm_group_id:
                logger.info(
                    "Retrieved alarm group ID",
                    tenant_id=tenant_id,
                    severity=severity,
                    camera_group_id=camera_group_id,
                    alarm_group_id=alarm_group_id,
                )
                return jsonify(
                    {"status": "ok", "alarm_group_id": alarm_group_id}
                )
            else:
                logger.warning(
                    "Alarm group ID not found in Redis",
                    tenant_id=tenant_id,
                    severity=severity,
                    camera_group_id=camera_group_id,
                )
                alarm_group_controller = self._ctrl_map.alarm_group

                # Convert timestamp from milliseconds to datetime
                from datetime import datetime

                try:
                    # Convert the timestamp (in milliseconds) to a datetime object
                    timestamp_ms = int(data["timestamp_utc"])
                    timestamp_datetime = datetime.fromtimestamp(
                        timestamp_ms / 1000
                    )
                except (ValueError, TypeError) as e:
                    logger.error(
                        "Failed to convert timestamp",
                        tenant_id=tenant_id,
                        severity=severity,
                        camera_group_id=camera_group_id,
                        timestamp=data["timestamp_utc"],
                        error=str(e),
                    )
                    return (
                        jsonify(
                            {
                                "error": f"Invalid timestamp format: {data['timestamp_utc']}"
                            }
                        ),
                        HTTPStatus.BAD_REQUEST,
                    )

                alarm_group = AlarmGroup(
                    id=alarm_group_id,
                    severity=severity,
                    camera_group_id=camera_group_id,
                    tenant_id=tenant_id,
                    start_time_utc=timestamp_datetime,
                    state=EventState.PENDING,
                    resolution=Resolution.OPEN,
                )
                try:
                    alarm_group_stored_id, created, severity = (
                        alarm_group_controller.create_alarm_group(alarm_group)
                    )
                    if severity == Severity.HIGH:
                        add_event_redis(
                            alarm_group_stored_id,
                            Severity.HIGH,
                            tenant_id,
                            camera_group_id,
                        )
                    else:
                        add_event_redis(
                            alarm_group_stored_id,
                            Severity.LOW,
                            tenant_id,
                            camera_group_id,
                        )
                    return (
                        jsonify(
                            {
                                "status": "ok",
                                "alarm_group_id": alarm_group_stored_id,
                            }
                        ),
                        HTTPStatus.OK,
                    )
                except Exception as e:
                    logger.error(
                        "Failed to create alarm group in database",
                        tenant_id=tenant_id,
                        severity=severity,
                        camera_group_id=camera_group_id,
                        error=str(e),
                    )
                    return (
                        jsonify(
                            {
                                "error": "Failed to create alarm group in database",
                                "details": str(e),
                            }
                        ),
                        HTTPStatus.INTERNAL_SERVER_ERROR,
                    )
        except redis.RedisError as e:
            logger.error(
                "Redis connection error",
                tenant_id=tenant_id,
                severity=severity,
                camera_group_id=camera_group_id,
                error=str(e),
            )
            return (
                jsonify(
                    {"error": "Redis connection error", "details": str(e)}
                ),
                HTTPStatus.INTERNAL_SERVER_ERROR,
            )
        except Exception as e:
            logger.error(
                "Unexpected error in create_alarm_group",
                tenant_id=tenant_id,
                severity=severity,
                camera_group_id=camera_group_id,
                error=str(e),
            )
            return (
                jsonify({"error": "Internal server error", "details": str(e)}),
                HTTPStatus.INTERNAL_SERVER_ERROR,
            )

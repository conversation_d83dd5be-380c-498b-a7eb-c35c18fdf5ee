import redis
from flask import Blueprint, request

from db_controller import DBControllerV1
from vision.services.src.event_processor.endpoints.VisionCommandInternalEndpoint import (
    VisionCommandInternalEndpoint,
)
from vision.services.src.event_processor.endpoints.VisionQueryAuthenticatedEndpoint import (
    VisionQueryAuthenticatedEndpoint,
)
from vision.services.src.event_processor.endpoints.VisionQueryInternalEndpoint import (
    VisionQueryInternalEndpoint,
)


def vision_http_server_blueprints(
    controller: DBControllerV1, redis_client: redis.Redis
):
    api = Blueprint("vision_http_server_endpoints", __name__)
    vision_query_authenticated_endpoint = VisionQueryAuthenticatedEndpoint(
        controller, redis_client
    )
    vision_query_internal_endpoint = VisionQueryInternalEndpoint(
        controller, redis_client
    )
    vision_command_internal_endpoint = VisionCommandInternalEndpoint(
        controller, redis_client
    )

    @api.route("/cameras/<camera_id>", methods=["GET"])
    def get_camera_info(camera_id):
        return vision_query_internal_endpoint.get_camera_info(camera_id)

    @api.route("/camera_group/<camera_group_id>", methods=["GET"])
    def get_pending_alarms_for_camera_group(camera_group_id):
        return (
            vision_query_internal_endpoint.get_pending_alarms_for_camera_group(
                camera_group_id
            )
        )

    @api.route("/alarm_group/list", methods=["POST"])
    def get_alarm_group_list():
        return vision_query_internal_endpoint.get_alarm_group_list()

    @api.route("/alarm_group/<alarm_group_id>/event", methods=["GET"])
    def get_alarm_group_event(alarm_group_id):
        return vision_query_internal_endpoint.get_alarm_group_event(
            alarm_group_id
        )

    @api.route("/getOperatorQueueCount", methods=["POST"])
    def get_operator_queue_count():
        return vision_query_internal_endpoint.get_operator_queue_count()

    @api.route("/addPendingAlarmsToSet", methods=["POST"])
    def add_pending_alarms_to_set():
        return vision_command_internal_endpoint.add_pending_alarms_to_set()

    @api.route(
        "/camera_group/<camera_group_id>/tenant/<tenant_id>/severity/<severity>",
        methods=["POST"],
    )
    def create_alarm_group(camera_group_id, tenant_id, severity):
        return vision_command_internal_endpoint.create_alarm_group(
            tenant_id, camera_group_id, severity
        )

    @api.route("/alarm_groups", methods=["GET"])
    def get_alarm_groups():
        query_params = request.args.to_dict()
        return vision_query_authenticated_endpoint.get_alarm_groups(
            query_params=query_params,
        )

    @api.route("/alarm_groups/<alarm_group_id>", methods=["GET"])
    def get_alarm_group_details(alarm_group_id: str):
        return vision_query_authenticated_endpoint.get_alarm_group_details(
            alarm_group_id=alarm_group_id,
        )

    @api.route("/alarm_groups/<alarm_group_id>/updates", methods=["GET"])
    def get_alarm_group_updates(alarm_group_id: str):
        return vision_query_authenticated_endpoint.get_alarm_group_updates(
            alarm_group_id=alarm_group_id,
        )

    return api

import argparse
import os

import prometheus_client as prom
import redis
import structlog
from flask import Flask, jsonify

from config import backend_config as config
from db_controller import DBControllerV1
from db_model_rds.rds_client import RDSClient
from vision.services.src.event_processor.endpoints.blueprint import (
    vision_http_server_blueprints,
)

logger = structlog.get_logger("hakimo", module="http_server_event_processor")


workers = int(os.environ.get("HTTP_WORKERS", 2))


def create_app():
    # Initialize database and Redis connections
    rds = RDSClient()
    db = rds.db_adapter
    read_db = rds.read_db_adapter
    ctrl_map = DBControllerV1(db)

    """Create and configure the Flask application"""
    app = Flask(__name__)

    # Initialize Redis connection
    redis_client = redis.Redis(
        host=config.HAIE.redis[
            "visionServiceName"
        ],  # Replace with your Redis host
        port=config.HAIE.redis["port"],  # Replace with your Redis port
        db=0,  # Default database
        decode_responses=True,
    )

    logger.info("HTTP server application configured")

    blueprint = vision_http_server_blueprints(ctrl_map, redis_client)
    app.register_blueprint(blueprint)

    # Register health check endpoints
    @app.route("/health", methods=["GET"])
    def health_check():
        return jsonify({"status": "ok"})

    @app.route("/readiness", methods=["GET"])
    def readiness_check():
        try:
            # Check database connection
            read_db.engine.execute("SELECT 1")

            # Check Redis connection
            redis_client.ping()

            return jsonify({"status": "ready"})
        except Exception as e:
            logger.error("Readiness check failed", error=str(e))
            return jsonify({"status": "not ready", "error": str(e)}), 503

    @app.route("/liveness", methods=["GET"])
    def liveness_check():
        return jsonify({"status": "alive"})

    return app


def run_with_gunicorn():
    """Entry point for gunicorn to run the HTTP server"""
    return create_app()


if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Event processor HTTP server")
    parser.add_argument(
        "--host",
        type=str,
        default="0.0.0.0",
        help="Host to bind the server to (default: 0.0.0.0)",
    )
    parser.add_argument(
        "--port",
        type=int,
        default=8080,
        help="Port to bind the server to (default: 8080)",
    )
    parser.add_argument(
        "--debug",
        action="store_true",
        help="Run the server in debug mode",
    )

    args = parser.parse_args()
    prom.start_http_server(8000)

    # Create and configure the Flask app
    app = create_app()

    # Start the Flask app
    app.run(
        host=args.host,
        port=args.port,
        debug=args.debug,
        use_reloader=False,
    )

import datetime
import typing

import structlog
from sqlalchemy import or_, select
from sqlalchemy.orm import aliased

from database.db_adapter import DBAdapter
from models_rds.alarm_types import AlarmTypes
from models_rds.cameras import Cameras
from models_rds.door_camera_params import DoorCameraParams
from models_rds.doors import Doors
from models_rds.employees import Employees
from models_rds.locations import Locations
from models_rds.raw_alarms import RawAlarms, get_default_partition_key_filter
from models_rds.raw_alarms_metadata import RawAlarmsMetadata
from models_rds.sop import SOP
from models_rds.tenants import Tenants

from .alarm_filters import AlarmFilters

ALARM_TIMESTAMP_INDEX = "ra_alarm_ts_tenant_part_key"
ALARM_NEIGHBOR_DESC_INDEX = "timestamp_desc_door_tenant_idx"
ALARM_NEIGHBOR_ASC_INDEX = "timestamp_door_tenant_idx"
ALARM_SOURCE_ENTITY_TIMESTAMP_INDEX = "source_entity_tenant_timestamp_desc_idx"
STATUS_INDEX = "tid_status_time_idx"
PRIMARY_INDEX = "PRIMARY"
FETCHED_ALARMS = typing.Tuple[
    RawAlarms,
    str,  # Alarm type
    typing.Optional[Doors],
    typing.Optional[Cameras],
    typing.Optional[Employees],
    str,  # tenant name
    typing.Optional[Locations],  # Door location
    typing.Optional[Locations],  # Camera location
    typing.Optional[RawAlarmsMetadata],
]
FETCHED_SINGLE_ALARM = typing.Tuple[
    RawAlarms,
    str,  # Alarm type
    typing.Optional[Doors],
    typing.Optional[Cameras],
    typing.Optional[Employees],
    typing.Optional[str],
    typing.Optional[str],
    typing.Optional[Locations],  # Door location
    typing.Optional[Locations],  # Camera location
    typing.Optional[RawAlarmsMetadata],
]
log = structlog.get_logger("hakimo", module="Alarm Fetcher")


class AlarmFetcher:
    def __init__(
        self, db: DBAdapter, read_db: typing.Optional[DBAdapter] = None
    ):
        self.db = db
        self.read_db = read_db
        if read_db is None:
            self.read_db = db

    def fetch_alarms(
        self,
        tenant_ids: typing.Optional[typing.Sequence[str]],
        filters: AlarmFilters,
        offset: typing.Optional[int] = 0,
        limit: typing.Optional[int] = 100,
        msp_locations: typing.Optional[typing.Sequence[int]] = None,
    ) -> typing.Sequence[FETCHED_ALARMS]:
        """Returns a sequence of tuples, each containing the raw alarm object,
        the alarm type and the doors object
        """
        door_location = aliased(Locations)
        cam_location = aliased(Locations)
        _db = self.read_db if self.read_db is not None else self.db
        with _db.get_session() as sess:
            q = (
                select(
                    RawAlarms,
                    AlarmTypes.alarm_type,
                    Doors,
                    Cameras,
                    Employees,
                    Tenants.name,
                    door_location,
                    cam_location,
                    RawAlarmsMetadata,
                )
                .join(
                    AlarmTypes,
                    RawAlarms.alarm_type_id == AlarmTypes.uuid,
                    isouter=True,
                )
                .join(Doors, RawAlarms.door_id == Doors.uuid, isouter=True)
                .join(
                    Cameras,
                    RawAlarms.source_entity_id == Cameras.uuid,
                    isouter=True,
                )
                .join(
                    Employees,
                    RawAlarms.employee_id == Employees.uuid,
                    isouter=True,
                )
                .join(
                    Tenants,
                    RawAlarms.tenant_id == Tenants.uuid,
                    isouter=True,
                )
                .join(
                    door_location,
                    door_location.id == Doors.location_id,
                    isouter=True,
                )
                .join(
                    cam_location,
                    cam_location.id == Cameras.location_id,
                    isouter=True,
                )
                .join(
                    RawAlarmsMetadata,
                    RawAlarms.uuid == RawAlarmsMetadata.raw_alarm_id,
                    isouter=True,
                )
            )
            range_end_at_utc = datetime.datetime.now(datetime.timezone.utc)
            partition_filter = get_default_partition_key_filter(
                start_at_utc=range_end_at_utc - datetime.timedelta(days=180),
                end_at_utc=range_end_at_utc,
            )
            if tenant_ids:
                q = q.filter(RawAlarms.tenant_id.in_(tenant_ids))
            if msp_locations:
                q = q.filter(Doors.location_id.in_(msp_locations))
            if filters.utc_time_interval:
                if filters.utc_time_interval[0]:
                    q = q.filter(
                        RawAlarms.alarm_timestamp_utc
                        >= filters.utc_time_interval[0]
                    )
                if filters.utc_time_interval[1]:
                    q = q.filter(
                        RawAlarms.alarm_timestamp_utc
                        <= filters.utc_time_interval[1]
                    )
                if (
                    filters.utc_time_interval[0]
                    and filters.utc_time_interval[1]
                ):
                    partition_filter = get_default_partition_key_filter(
                        start_at_utc=filters.utc_time_interval[0],
                        end_at_utc=filters.utc_time_interval[1],
                    )

            if filters.location:
                # positive filter
                if filters.location[1]:
                    q = q.filter(
                        or_(
                            door_location.id.in_(filters.location[0]),
                            cam_location.id.in_(filters.location[0]),
                        )
                    )
                else:
                    q = q.filter(
                        or_(
                            door_location.id.not_in(filters.location[0]),
                            cam_location.id.not_in(filters.location[0]),
                        )
                    )

            if filters.tenant:
                if filters.tenant[1]:
                    q = q.filter(Tenants.uuid.in_(filters.tenant[0]))
                else:
                    q = q.filter(Tenants.uuid.not_in(filters.tenant[0]))
            # TODO: Deprecate door filter after frontend changes
            if filters.door and filters.door_uuid:
                log.warning(
                    "Both door name and door uuid filters are present, Only door name filter will be used"
                )
            if filters.employee_uuid:
                if filters.employee_uuid[1]:
                    q = q.filter(Employees.uuid.in_(filters.employee_uuid[0]))
                else:
                    q = q.filter(
                        Employees.uuid.not_in(filters.employee_uuid[0])
                    )

            if filters.door:
                if filters.door[1]:
                    q = q.filter(Doors.door_name.in_(filters.door[0]))
                else:
                    q = q.filter(Doors.door_name.not_in(filters.door[0]))
            elif filters.door_uuid:
                if filters.door_uuid[1]:
                    q = q.filter(Doors.uuid.in_(filters.door_uuid[0]))
                else:
                    q = q.filter(Doors.uuid.not_in(filters.door_uuid[0]))
            if filters.source_entity:
                if filters.source_entity[1]:
                    q = q.filter(
                        or_(
                            Doors.door_name.in_(filters.source_entity[0]),
                            Cameras.name.in_(filters.source_entity[0]),
                        )
                    )
                else:
                    q = q.filter(
                        or_(
                            Doors.door_name.not_in(filters.source_entity[0]),
                            Cameras.name.not_in(filters.source_entity[0]),
                        )
                    )
            if filters.source_entity_id:
                if filters.source_entity_id[1]:
                    q = q.filter(
                        or_(
                            Doors.uuid.in_(filters.source_entity_id[0]),
                            Cameras.uuid.in_(filters.source_entity_id[0]),
                        )
                    )
                else:
                    q = q.filter(
                        or_(
                            Doors.uuid.not_in(filters.source_entity_id[0]),
                            Cameras.uuid.not_in(filters.source_entity_id[0]),
                        )
                    )
            if filters.status:
                if filters.status[1]:
                    q = q.filter(
                        RawAlarms.current_status.in_(filters.status[0])
                    )
                else:
                    q = q.filter(
                        RawAlarms.current_status.not_in(filters.status[0])
                    )
            if filters.tap:
                if filters.tap[0]:
                    q = q.filter(
                        RawAlarms.true_alarm_probability >= filters.tap[0]
                    )
                if filters.tap[1]:
                    q = q.filter(
                        RawAlarms.true_alarm_probability <= filters.tap[1]
                    )
            if filters.source:
                q = q.filter(RawAlarms.source_system.in_(filters.source))
            if filters.display_only:
                q = q.filter(RawAlarms.display == 1)
            if filters.alarm_type and filters.alarm_type_uuid:
                log.warning(
                    "Both Alarm type and alarm type uuid filters are present, Only Alarm type filter will be used"
                )
            if filters.alarm_type:
                if filters.alarm_type[1]:
                    q = q.filter(
                        AlarmTypes.alarm_type.in_(filters.alarm_type[0])
                    )
                else:
                    q = q.filter(
                        AlarmTypes.alarm_type.not_in(filters.alarm_type[0])
                    )
            elif filters.alarm_type_uuid:
                if filters.alarm_type_uuid[1]:
                    q = q.filter(
                        AlarmTypes.uuid.in_(filters.alarm_type_uuid[0])
                    )
                else:
                    q = q.filter(
                        AlarmTypes.uuid.not_in(filters.alarm_type_uuid[0])
                    )

            # Adding manned/unmanned location filters
            if filters.showMannedLocationAlarms is not None:
                q = q.filter(
                    door_location.is_manned == filters.showMannedLocationAlarms
                )

            if filters.alarm_uuids:
                q = q.filter(RawAlarms.uuid.in_(filters.alarm_uuids))

            q = q.filter(partition_filter)
            if filters.order_desc:
                q = q.order_by(RawAlarms.alarm_timestamp_utc.desc())
            else:
                q = q.order_by(RawAlarms.alarm_timestamp_utc.asc())
            if (
                (filters.door_uuid and filters.door_uuid[1])
                or (filters.door and filters.door[1])
                or msp_locations
            ):
                # An index which has door and timestamp information together
                # would work best here, unless it's a negative filter
                q = q.with_hint(
                    RawAlarms, f"USE INDEX ({ALARM_NEIGHBOR_DESC_INDEX})"
                )
            elif filters.status and filters.status[1]:
                q = q.with_hint(RawAlarms, f"USE INDEX ({STATUS_INDEX})")
            elif filters.alarm_uuids:
                q = q.with_hint(RawAlarms, f"USE INDEX ({PRIMARY_INDEX})")
            else:
                # When we do not filter by door or location, timestamp is usually
                # the best index. Sometimes the query optimizer does not pick it up.
                q = q.with_hint(
                    RawAlarms, f"USE INDEX ({ALARM_TIMESTAMP_INDEX})"
                )
            if offset:
                q = q.offset(offset)
            # If limit is None, don't apply a limit, and retrieve all alarms
            if limit is not None:
                q = q.limit(limit)
            rs = sess.execute(q).all()
            sess.expunge_all()
            return rs

    def fetch_alarm(
        self, alarm_id: str, partition_key: int = None
    ) -> FETCHED_SINGLE_ALARM:
        source_entity_cam = aliased(Cameras)
        door_mapped_cam = aliased(Cameras)
        door_location = aliased(Locations)
        cam_location = aliased(Locations)
        _db = self.read_db if self.read_db is not None else self.db
        with _db.get_session() as sess:
            q = (
                select(
                    RawAlarms,
                    AlarmTypes.alarm_type,
                    Doors,
                    source_entity_cam,
                    Employees,
                    door_mapped_cam.name,
                    SOP.sop,
                    door_location,
                    cam_location,
                    RawAlarmsMetadata,
                )
                .join(AlarmTypes, RawAlarms.alarm_type_id == AlarmTypes.uuid)
                .join(
                    SOP,
                    (
                        (RawAlarms.alarm_type_id == SOP.alarm_type_id)
                        & (
                            (RawAlarms.tenant_id == SOP.tenant_id)
                            | SOP.tenant_id.is_(None)
                        )
                    ),
                    isouter=True,
                )
                .join(Doors, RawAlarms.door_id == Doors.uuid, isouter=True)
                .join(
                    source_entity_cam,
                    RawAlarms.source_entity_id == source_entity_cam.uuid,
                    isouter=True,
                )
                .join(
                    Employees,
                    RawAlarms.employee_id == Employees.uuid,
                    isouter=True,
                )
                .join(
                    DoorCameraParams,
                    Doors.uuid == DoorCameraParams.door_id,
                    isouter=True,
                )
                .join(
                    door_mapped_cam,
                    DoorCameraParams.camera_id == door_mapped_cam.uuid,
                    isouter=True,
                )
                .join(
                    door_location,
                    door_location.id == Doors.location_id,
                    isouter=True,
                )
                .join(
                    cam_location,
                    cam_location.id == source_entity_cam.location_id,
                    isouter=True,
                )
                .join(
                    RawAlarmsMetadata,
                    RawAlarms.uuid == RawAlarmsMetadata.raw_alarm_id,
                    isouter=True,
                )
            ).filter(RawAlarms.uuid == alarm_id)
            if partition_key:
                q = q.filter(RawAlarms.partition_key == partition_key)
            else:
                q = q.filter(get_default_partition_key_filter())
            rs = sess.execute(q).first()
            sess.expunge_all()
            return rs

    def get_neighboring_alarms(
        self,
        alarm: RawAlarms,
        before_limit: int = 5,
        after_limit: int = 5,
        before_time: typing.Optional[datetime.timedelta] = None,
        after_time: typing.Optional[datetime.timedelta] = None,
        partition_key: typing.Optional[int] = None,
    ) -> typing.Tuple[typing.Sequence[typing.Tuple[RawAlarms, str]], int]:
        """Returns neighboring alarms + the index of the current alarm if it were
        to be inserted into the list (according to alarm timestamp)
        """
        _db = self.read_db if self.read_db is not None else self.db
        with _db.get_session() as sess:
            before_query = (
                select(RawAlarms, AlarmTypes.alarm_type)
                .join(
                    AlarmTypes,
                    AlarmTypes.uuid == RawAlarms.alarm_type_id,
                    isouter=True,
                )
                .filter(RawAlarms.tenant_id == alarm.tenant_id)
                .filter(RawAlarms.uuid != alarm.uuid)
                .order_by(RawAlarms.alarm_timestamp_utc.desc())
            ).limit(before_limit)
            if before_time is not None:
                before_query = before_query.filter(
                    RawAlarms.alarm_timestamp_utc.between(
                        alarm.alarm_timestamp_utc - before_time,
                        alarm.alarm_timestamp_utc,
                    )
                )
            else:
                before_query = before_query.filter(
                    RawAlarms.alarm_timestamp_utc <= alarm.alarm_timestamp_utc
                )
            if alarm.door_id is not None:
                before_query = before_query.filter(
                    RawAlarms.door_id == alarm.door_id
                ).with_hint(
                    RawAlarms, f"USE INDEX ({ALARM_NEIGHBOR_DESC_INDEX})"
                )
            elif alarm.source_entity_id is not None:
                before_query = before_query.filter(
                    RawAlarms.source_entity_id == alarm.source_entity_id
                ).with_hint(
                    RawAlarms,
                    f"USE INDEX ({ALARM_SOURCE_ENTITY_TIMESTAMP_INDEX})",
                )
            if partition_key:
                before_query = before_query.filter(
                    RawAlarms.partition_key == partition_key
                )
            elif before_time is not None:
                before_query = before_query.filter(
                    get_default_partition_key_filter(
                        alarm.alarm_timestamp_utc - before_time,
                        alarm.alarm_timestamp_utc,
                    )
                )
            else:
                before_query = before_query.filter(
                    get_default_partition_key_filter()
                )
            ret = sess.execute(before_query).all()
            num_before = len(ret)
            after_query = (
                select(RawAlarms, AlarmTypes.alarm_type)
                .join(
                    AlarmTypes,
                    AlarmTypes.uuid == RawAlarms.alarm_type_id,
                    isouter=True,
                )
                .filter(RawAlarms.uuid != alarm.uuid)
                .filter(RawAlarms.tenant_id == alarm.tenant_id)
                .order_by(RawAlarms.alarm_timestamp_utc)
            ).limit(after_limit)
            if after_time is not None:
                after_query = after_query.filter(
                    RawAlarms.alarm_timestamp_utc.between(
                        alarm.alarm_timestamp_utc,
                        alarm.alarm_timestamp_utc + after_time,
                    )
                )
            else:
                after_query = after_query.filter(
                    RawAlarms.alarm_timestamp_utc >= alarm.alarm_timestamp_utc
                )
            if alarm.door_id is not None:
                after_query = after_query.filter(
                    RawAlarms.door_id == alarm.door_id
                ).with_hint(
                    RawAlarms, f"USE INDEX ({ALARM_NEIGHBOR_ASC_INDEX})"
                )
            elif alarm.source_entity_id is not None:
                after_query = after_query.filter(
                    RawAlarms.source_entity_id == alarm.source_entity_id
                ).with_hint(
                    RawAlarms,
                    f"USE INDEX ({ALARM_SOURCE_ENTITY_TIMESTAMP_INDEX})",
                )
            if partition_key:
                after_query = after_query.filter(
                    RawAlarms.partition_key == partition_key
                )
            elif after_time is not None:
                after_query = after_query.filter(
                    get_default_partition_key_filter(
                        alarm.alarm_timestamp_utc,
                        alarm.alarm_timestamp_utc + after_time,
                    )
                )
            else:
                after_query = after_query.filter(
                    get_default_partition_key_filter()
                )
            ret.extend(sess.execute(after_query).all())
            sess.expunge_all()
            return ret, num_before

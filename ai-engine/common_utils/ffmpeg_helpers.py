"""ffmpeg implementation methods for various HIP processes"""

import datetime
import fractions
import json
import os
import os.path as osp
import shutil
import subprocess
import tempfile
import typing
from subprocess import CalledProcessError, TimeoutExpired

import structlog

from common_utils.playback_helper import HLS_PLAYBACK_SEGMENT_DURATION_SECONDS
from common_utils.string_helpers import sanitize_rtsp_url
from common_utils.typing_helpers import NUMBER
from errors import RetryableError
from integ.vms.video_interval import VideoInterval
from integ.vms.video_result import VideoResult

log = structlog.get_logger("hakimo", module="ffmpeg helper")
RTSP_CONNECT_TIMEOUT_SECONDS = 30


def video_duration_millis(video_path: str) -> typing.Tuple[float, typing.Any]:
    res = get_video_duration_ffprobe(video_path)
    if res["error"]:
        return -1, res["error"]

    return res["duration"] * 1000, None


def slow_down_video(input_path: str, output_path: str):
    # FFmpeg command to slow down video and audio by half (0.5x speed)
    ffmpeg_path = _get_ffmpeg_path()
    command = [
        ffmpeg_path,
        "-i",
        input_path,
        "-vf",
        "setpts=2.0*PTS",
        "-filter:a",
        "atempo:0.5",
        "-y",
        output_path,
    ]
    try:
        # Run the command
        res = subprocess.run(
            command,
            check=True,
            stdout=subprocess.PIPE,
            stderr=subprocess.DEVNULL,
        )
        log.debug("std output", output=res.stdout)
    except CalledProcessError as cpe:
        log.warning(
            "Invocation error. Failed to capture frame ts.",
            command=cpe.cmd,
            error=cpe.stderr,
            out=cpe.stdout,
            ret_code=cpe.returncode,
        )


def get_frame_ts(video_path: str) -> typing.Optional[typing.List[NUMBER]]:
    try:
        res = subprocess.run(
            [
                "ffprobe",
                "-v",
                "error",
                "-select_streams",
                "v:0",
                "-i",
                video_path,
                "-show_entries",
                "packet=pts_time",
                "-of",
                "csv=p=0",
            ],
            stdout=subprocess.PIPE,
            stderr=subprocess.DEVNULL,
            check=True,
        )
        frame_ts = res.stdout
        log.debug("std output", output=res.stdout)
        return sorted(
            [float(i) for i in frame_ts.decode("utf-8").split("\n") if i]
        )
    except CalledProcessError as cpe:
        log.warning(
            "Invocation error. Failed to capture frame ts.",
            command=cpe.cmd,
            error=cpe.stderr,
            out=cpe.stdout,
            ret_code=cpe.returncode,
        )
    return None


def get_ffmpeg_frame_count(video_path: str) -> typing.Optional[int]:
    """Check the metadata using ffprobe and return the number of frames
    in the video. This might not be accurate, but in practice has been
    better than using the cv2.VideoCapture object to get the total
    number of frames.
    """
    try:
        res = subprocess.run(
            [
                "ffprobe",
                "-v",
                "error",
                "-select_streams",
                "v:0",
                "-count_packets",
                "-show_entries",
                "stream=nb_read_packets",
                "-print_format",
                "json",
                video_path,
            ],
            stdout=subprocess.PIPE,
            stderr=subprocess.DEVNULL,
            check=True,
        )
        vid_data = res.stdout
        return int(
            json.loads(vid_data.decode("utf-8"))["streams"][0][
                "nb_read_packets"
            ]
        )
    except (ValueError, json.JSONDecodeError):
        return None
    except CalledProcessError as cpe:
        log.info(
            "Invocation error. Failed to capture framecount.",
            command=cpe.cmd,
            error=cpe.stderr,
            out=cpe.stdout,
            ret_code=cpe.returncode,
        )
        return None


def get_metadata_ffprobe(
    video_path: str,
) -> typing.Dict[str, typing.Any]:
    """
    Returns all the metadata and video format parameters available via ffprobe
    for the given video scrape

    Returns typing.Dict[str, str]: Dict containing
        1. All metadata parameters of the video stream in ffprobe output
        2. All format parameters of the video stream in ffprobe output
    Note: This dict may or may not contain parameters like creation_timestamp.
    """
    try:
        res = subprocess.run(
            [
                "ffprobe",
                "-v",
                "error",
                "-print_format",
                "json",
                "-show_streams",
                "-show_format",
                "-show_chapters",
                "-show_private_data",
                video_path,
            ],
            stdout=subprocess.PIPE,
            stderr=subprocess.DEVNULL,
            check=True,
        )
        vid_data = res.stdout
        return {"error": None, "data": json.loads(vid_data.decode("utf-8"))}
    except ValueError:
        return {"error": "ffprobe could not get video metadata.", "data": None}
    except CalledProcessError as cpe:
        log.info(
            "Invocation error. Failed to get video metadata.",
            command=cpe.cmd,
            error=cpe.stderr,
            out=cpe.stdout,
            ret_code=cpe.returncode,
        )
        return {"error": "ffprobe could not get video metadata.", "data": None}


def get_video_duration_ffprobe(video_path: str) -> typing.Dict:
    """Helper function to return the duration as a key in seconds,
    also returns the stdout as output and an error message which
    is str or None
    """

    # todo: this might be better tested
    # https://github.com/gbstack/ffprobe-python
    try:
        res = subprocess.run(
            [
                "ffprobe",
                "-v",
                "error",
                "-show_entries",
                "format=duration",
                "-of",
                "default=noprint_wrappers=1:nokey=1",
                video_path,
            ],
            stdout=subprocess.PIPE,
            stderr=subprocess.DEVNULL,
            check=True,
        )
        duration = float(res.stdout)
        return {"error": None, "duration": duration, "output": res.stdout}
    except ValueError:
        return {
            "error": "ffprobe returned non-float value",
            "duration": 0,
            "output": res.stdout,
        }
    except CalledProcessError as cpe:
        log.info(
            "Invocation error. Failed to get video duration.",
            command=cpe.cmd,
            ret_code=cpe.returncode,
            out=cpe.stdout,
            error=cpe.stderr,
        )
        return {
            "error": "ffprobe failed to get video duration.",
            "duration": 0,
            "output": cpe.stdout,
        }


def save_stream(r, local_filename: str):
    with open(local_filename, "wb") as f:
        for chunk in r.iter_content(chunk_size=1024):
            # filter out keep-alive new chunks
            if chunk:
                f.write(chunk)
    return local_filename


def concat_videos(filepaths, outfile_path, reencode=False):
    if reencode:
        # use concate filter
        num_videos = len(filepaths)
        cmd = ["ffmpeg", "-y"]
        for f in filepaths:
            cmd.extend(["-i", f])
        cmd.extend(["-filter_complex"])
        filter_graph = ""
        for i in range(num_videos):
            filter_graph += f"[{i}:v] "
        filter_graph += f"concat=n={num_videos}:v=1 [v]"
        cmd.append(filter_graph)
        cmd.extend(["-map", "[v]", outfile_path])
        filename = "dummy_file.txt"
    else:
        with tempfile.NamedTemporaryFile(
            mode="w", delete=False, suffix=".txt"
        ) as fout:
            for filename in filepaths:
                fout.write(f"file '{filename}'\n")

        filename = fout.name
        cmd = [
            "ffmpeg",
            "-y",
            "-f",
            "concat",
            "-safe",
            "0",
            "-i",
            filename,
            "-c",
            "copy",
            "-c:a",
            "aac",
            outfile_path,
        ]
    try:
        result = subprocess.run(
            cmd,
            check=True,
            capture_output=True,
        )
        log.debug(
            "concating files",
            payload_str=result.stdout.decode("utf-8"),
            filepaths=filepaths,
            outfile=outfile_path,
        )
        return outfile_path
    finally:
        try:
            os.unlink(filename)
        except OSError:
            pass


def cut_video(
    video_filepath: str,
    output_filepath: str,
    start_cut: float = 0,
    duration: typing.Optional[float] = None,
) -> bool:
    """Takes an input video file, and cuts the starting start_cut seconds
    from it, for an optional length - duration. If duration is not specified,
    only the first start_cut seconds are removed. If start_cut is not specified,
    the first duration seconds are KEPT.

    Args:
        start_cut (float, optional): time to cut from beginning of the video in seconds.
            Defaults to 0.
        duration (typing.Optional[float], optional): Duration of video to be kept.
            Defaults to None.

    Returns:
        bool: True if cut was successful, False otherwise
    """
    if start_cut == 0 and duration is None:
        # nothing to do, just copy the video
        shutil.copy(video_filepath, output_filepath)
        return True
    log.debug(
        "Cutting video",
        video_path=video_filepath,
        start_cut=start_cut,
        duration=duration,
    )
    metadata = get_metadata_ffprobe(video_filepath)["data"]
    if "streams" not in metadata:
        log.error(
            "Unable to get metadata for cutting video fragment",
            video_file=video_filepath,
            metadata=metadata,
            start_cut=start_cut,
            duration=duration,
        )
        return False
    # Get metadata from first stream
    fps = float(fractions.Fraction(metadata["streams"][0]["r_frame_rate"]))
    time_base = float(
        1 / fractions.Fraction(metadata["streams"][0]["time_base"])
    )
    video_length_secs = get_video_duration_ffprobe(video_filepath)["duration"]
    if start_cut > video_length_secs:
        raise ValueError("Cannot cut more than the length of input")

    time_str = f"{int(start_cut//3600):02d}:{int(start_cut//60):02d}:{start_cut%60:.03f}"
    cmd = ["ffmpeg", "-y"]
    cmd.extend(
        [
            "-ss",
            time_str,
            "-i",
            video_filepath,
        ]
    )
    if duration is not None:
        if start_cut > video_length_secs:
            raise ValueError("Cannot cut more than the length of input")
        duration_str = f"{int(duration//3600):02d}:{int(duration//60):02d}:{int(duration%60):02d}"
        cmd.extend(["-t", duration_str])
    # Reencoding here is necessary
    cmd.extend(
        [
            "-c",
            "copy",
            "-vsync",
            "passthrough",
            "-video_track_timescale",
            str(time_base),
            "-r",
            str(fps),
            output_filepath,
        ]
    )
    result = subprocess.run(
        cmd,
        capture_output=True,
    )
    if result.returncode:
        log.error(
            "Problem cutting video file",
            stdout=result.stdout,
            stderr=result.stderr,
        )
        return False
    return True


def get_eagleeye_video_from_rtsp_url(
    rtsp_url: str,
    vid_duration: int,
    video_output: str,
    start_time: float,
    end_time: float,
) -> typing.Tuple[bool, str, float, float]:
    """
    Function to get the video fragment directly from RTSP URL of the stream
    """
    log.debug(
        "Running ffmpeg with params ",
        vid_duration=vid_duration,
        video_path=video_output,
        operation="copy",
        start_time=start_time,
        end_time=end_time,
    )
    command = [
        "ffmpeg",
        "-i",
        rtsp_url,  # Input RTSP URL
        "-t",
        str(vid_duration),
        "-vcodec",
        "mjpeg",
        "-an",
        "-f",
        "mp4",
        "-rtsp_transport",
        "tcp",
        video_output,  # Output file path
    ]
    actual_start_time = start_time
    actual_end_time = end_time
    try:
        subprocess.run(command, check=True)
        log.debug(f"Stream saved successfully", video_output=video_output)
    except subprocess.CalledProcessError as e:
        raise RetryableError(f"Ffmpeg process Error: {str(e)}")
    except Exception as e:
        log.error(
            "Unknown exception in saving RTSP Stream to video file", exc_info=e
        )
        raise RetryableError(
            "Unknown exception in saving RTSP Stream to video file"
        )
    return True, video_output, actual_start_time, actual_end_time


def get_video_fragment_from_stream(
    stream_resp, video_output, t_time, actual_start_time, end_time
):
    """
    Function to get the video fragment from a RPC stream based on the config passed
    """
    log.debug(
        "Running ffmpeg with params ",
        time=t_time,
        video_path=video_output,
        segment_type="fmp4",
        input="pipe:0",
        operation="copy",
    )
    actual_end_time = end_time
    with open(video_output + "_ffmpeg_log", "w") as f:
        ffmpeg_proc = subprocess.Popen(
            [
                "ffmpeg",
                "-y",
                "-i",
                "pipe:0",
                "-hls_segment_type",
                "fmp4",
                "-t",
                str(t_time),
                "-c:v",
                "copy",
                video_output,
            ],
            stdin=subprocess.PIPE,
            stdout=f,
            stderr=subprocess.STDOUT,
        )
        for chunk in stream_resp.iter_content(chunk_size=1000):
            # filter out keep-alive new chunks
            if not chunk:
                continue

            if ffmpeg_proc.poll() is not None:
                break
            try:
                ffmpeg_proc.stdin.write(chunk)
            except BrokenPipeError:
                log.debug(
                    "FFMPEG pipe broken", exit_code=ffmpeg_proc.returncode
                )
                break

    try:
        ffmpeg_proc.stdin.close()
    except BrokenPipeError:
        log.warning(
            "FFMPEG pipe broken on ffmpeg close",
            exit_code=ffmpeg_proc.returncode,
        )

    with open(video_output + "_ffmpeg_log") as f:
        log.info("FFMPEG logs", ffmpeg_logs=f.read())
    try:
        ffmpeg_proc.wait(timeout=10)
    except subprocess.TimeoutExpired as err:
        log.exception("FFMPEG timeout", exc_info=err)
        ffmpeg_proc.terminate()
        raise RetryableError("FFMPEG timeout. Retrying...") from err

    log.info("FFMPEG exit code", exit_code=ffmpeg_proc.returncode)
    if ffmpeg_proc.returncode:
        log.error("FFMPEG error", exit_code=ffmpeg_proc.returncode)
        return "", False, actual_end_time

    if not osp.exists(video_output):
        raise RetryableError("Unknown ffmpeg exception - must retry")
    res = get_video_duration_ffprobe(video_output)
    if res["error"] is None:
        duration = res["duration"]
        log.info("FFMPEG viedo length", video_length=duration)
        actual_end_time = actual_start_time + (int(duration) * 1000)
    else:
        log.error("ffprobe error", output=res["output"], error=res["error"])
    if abs(t_time - duration) / t_time > 0.3:
        log.warning(
            "Alarm video has incorrect duration",
            expected_duration=t_time,
            actual_duration=duration,
        )
        raise RetryableError("Incorrect video duration")
    return video_output, True, actual_end_time


def deduplicate_fragments(
    fragments: typing.Sequence[VideoResult],
) -> typing.List[VideoResult]:
    """Takes a list of video result objects, obtained from multiple scrapes from a VMS,
    and deduplicates overlapping sections of video. This calls ffmpeg to cut fragments
    to generate intermediate files and returns those file paths to the caller. The resulting
    files will not have any overlap in time. In cases where one or more fragments has overlap
    with other fragments, only the video from the fragment with the earliest start time
    is kept.

    Returns:
        typing.List[VideoResult]:New video result objects with cut videos (where required)
    """
    fragments = sorted(fragments, key=lambda x: x.interval.start)
    if not fragments:
        return []
    out_frags = [fragments[0]]
    timeline = [fragments[0].interval]
    for frag in fragments[1:]:
        overlap_secs = (
            min(frag.interval.end, timeline[-1].end)
            - max(frag.interval.start, timeline[-1].start)
        ).total_seconds()
        if overlap_secs >= 0:
            # overlap
            timeline[-1].end = max(timeline[-1].end, frag.interval.end)
        else:
            timeline.append(frag.interval)
        input_filename, input_filext = osp.splitext(
            osp.basename(frag.filename)
        )
        out_filepath = osp.join(
            osp.dirname(frag.filename), input_filename + "_cut" + input_filext
        )
        # Here we need to cut out the first overlap_secs from the video, or zero
        # if no overlap
        cut_duration = max(0, overlap_secs)
        if cut_duration == 0:
            out_frags.append(frag)
        elif (
            cut_duration
            == (frag.interval.end - frag.interval.start).total_seconds()
        ):
            # We can just drop the entire video here
            continue
        else:
            success = cut_video(
                frag.filename, out_filepath, start_cut=cut_duration
            )
            if not success:
                raise Exception("Unable to cut video for deoverlapping")
            out_frags.append(
                VideoResult(
                    frag.requested,
                    VideoInterval(
                        frag.interval.start
                        + datetime.timedelta(seconds=cut_duration),
                        frag.interval.end,
                    ),
                    out_filepath,
                    None,
                )
            )
    return out_frags


def fill_in_gaps(
    fragments: typing.List[VideoResult],
) -> typing.List[VideoResult]:
    """Assumes fragments are non overlapping
    Takes a list of fragments and fills in gaps by duplicating the last frame
    of the preceding fragment. Returns a list of the 'gap' videos to be used during
    concatenation if needed
    """
    gaps = []
    if len(fragments) <= 1:
        # Gaps possible only if more than 1 fragment
        return fragments
    fragments.sort(key=lambda x: x.interval.start)
    for idx, next_frag in enumerate(fragments[1:]):
        prev_frag = fragments[idx]
        if (
            next_frag.interval.start - prev_frag.interval.end
            > datetime.timedelta(seconds=1)
        ):
            in_file_base, in_file_ext = osp.splitext(prev_frag.filename)
            filename = osp.join(
                osp.dirname(prev_frag.filename),
                in_file_base + "_gap_fill" + in_file_ext,
            )
            img_file = osp.join(
                osp.dirname(prev_frag.filename),
                in_file_base + "_img" + ".png",
            )
            subprocess.run(
                [
                    "ffmpeg",
                    "-y",
                    "-sseof",
                    "-3",
                    "-i",
                    prev_frag.filename,
                    "-vsync",
                    "0",
                    "-update",
                    "true",
                    img_file,
                ],
                check=True,
            )
            gap_secs = (
                next_frag.interval.start - prev_frag.interval.end
            ).total_seconds()
            subprocess.run(
                [
                    "ffmpeg",
                    "-y",
                    "-loop",
                    "1",
                    "-i",
                    img_file,
                    "-c:v",
                    "libx264",
                    "-t",
                    str(int(gap_secs)),
                    filename,
                ],
                check=True,
            )
            gaps.append(
                VideoResult(
                    VideoInterval(
                        start=prev_frag.interval.end,
                        end=next_frag.interval.start,
                    ),
                    VideoInterval(
                        start=prev_frag.interval.end,
                        end=next_frag.interval.start,
                    ),
                    filename,
                    None,
                )
            )
    return gaps


def rtsp_to_mediamtx_stream(
    rtsp_url: typing.Optional[str],
    destination_folder: str,
    mediamtx_server_url: str,
    mediamtx_server_port: int,
    stream_name: str,
) -> subprocess.Popen:
    """Return a subprocess handle that publishes the live stream
    (from the given RTSP url) to the configured MediaMTX Server.
    This method spawns the ffmpeg subprocess and returns its handle to the caller. It is
    responsibility of the caller to verify if the spawned process is
    running successfully and to wait on the process and release any
    resources held by the process."""

    # Sample ffmpeg command:
    # ffmpeg - stream_loop - 1 - i
    # 'rtsps://rtsp.svc.johan.yoursix.cloud:443?token=v2_OHjS3jKHtYKrIfS0WejTIg' - rtsp_transport
    # tcp - c
    # copy - f
    # rtsp
    # 'rtsp://mediamtx.mediamtx.svc.cluster.local:8554/sagaratest01'
    if not rtsp_url:
        log.error(
            "No RTSP Url given for MediaMtx Stream Publishing",
            rtsp_url=rtsp_url,
            stream_name=stream_name,
        )
        raise Exception("No RTSP Url given for MediaMtx Stream Publishing")

    ffmpeg_path = _get_ffmpeg_path()
    destination_folder = destination_folder.rstrip("/")
    os.makedirs(destination_folder, exist_ok=True)
    f_args = [
        ffmpeg_path,
        "-hide_banner",
        "-loglevel",
        "debug",
        "-stream_loop",
        "-1",
        "-i",
        rtsp_url,
        "-rtsp_transport",
        "tcp",
        "-c",
        "copy",
        "-f",
        "rtsp",
        f"rtsp://{mediamtx_server_url}:{mediamtx_server_port}/{stream_name}",
    ]
    log_file = os.path.join(destination_folder, "hls_stream.log")
    log.debug(
        "Spawning ffmpeg to generate MediaMtx based Livestream.",
        log_file=log_file,
    )

    with open(log_file, "wb") as flog:
        return subprocess.Popen(
            f_args,
            stdout=flog,
            stderr=flog,
            cwd=destination_folder,
        )


def rtsp_to_hls(
    rtsp_url: str,
    destination_folder: str,
    segment_duration_seconds: int = 2,
    segment_list_size: int = 15,
    segment_count_to_wrap_after=20,
) -> subprocess.Popen:
    """Return a subprocess handle that provides an HTTP live stream (HLS)
    (from the given RTSP url). The playlist file and segment files
    are generated at the given destination folder. This method spawns
    a subprocess and returns its handle to the caller. It is
    responsibility of the caller to verify if the spawned process is
    running successfully and to wait on the process and release any
    resources held by the process."""

    # sample ffmpeg command
    # ffmpeg -hide_banner -loglevel warning -fflags nobuffer -rtsp_transport tcp \
    # -i 'rtsp://admin:<pw>@*************:554/Streaming/Channels/101'  \
    # -vsync 0 -copyts -codec copy \
    # -hls_flags append_list \
    # -f segment -segment_list_flags live -segment_time 2  -segment_list_size 5  \
    # -segment_format mpegts -segment_list /data/rtsp_videos/camera_1/livestream/index.m3u8 \
    # -segment_list_type m3u8  \
    # /data/rtsp_videos/camera_1/livestream/%3d.ts

    ffmpeg_path = _get_ffmpeg_path()
    destination_folder = destination_folder.rstrip("/")
    os.makedirs(destination_folder, exist_ok=True)

    f_args = [
        ffmpeg_path,
        "-hide_banner",
        "-loglevel",
        "warning",
        "-fflags",
        "nobuffer",
        "-rtsp_transport",
        "tcp",
        "-i",
        rtsp_url,
        "-vsync",
        "0",
        "-copyts",
        "-codec",
        "copy",
        "-f",
        "segment",
        "-hls_flags",
        "append_list",
        "-segment_list_flags",
        "live",
        "-segment_wrap",
        str(segment_count_to_wrap_after),
        "-segment_time",
        str(segment_duration_seconds),
        "-segment_list_size",
        str(segment_list_size),
        "-segment_format",
        "mpegts",
        "-segment_list_type",
        "m3u8",
        "-segment_list",
        f"{destination_folder}/index.m3u8",
        f"{destination_folder}/%3d.ts",
    ]
    log_file = os.path.join(destination_folder, "hls_stream.log")
    log.debug(
        "Spawning ffmpeg to generate HLS stream.",
        destination_folder=destination_folder,
        log_file=log_file,
    )

    with open(log_file, "wb") as flog:
        return subprocess.Popen(
            f_args,
            stdout=flog,
            stderr=flog,
            cwd=destination_folder,
        )


# TODO: this needs to be merged with "rtsp_to_hls". There are some
# options that are specific to playback which might not be suitable
# for livestreaming.  Also the command invocation is slightly
# different. Merge the methods and any variations can be taken care
# via parameters passed to the function.


# With this invocation when the running invocation is killed and a new
# invocation is started any existing playlist file is used to add
# details of new video segments fetched.
def rtsp_to_hls_playback(
    rtsp_url: str,
    destination_folder: str,
    playlist_file_name: str,
    extra_rtsp_args: typing.Optional[typing.List[str]] = None,
) -> subprocess.Popen:
    """Return a subprocess handle that provides an HTTP live stream (HLS)
    (from the given RTSP url). The playlist file and segment files
    are generated at the given destination folder. This method spawns
    a subprocess and returns its handle to the caller. It is
    responsibility of the caller to verify if the spawned process is
    running successfully and to wait on the process and release any
    resources held by the process."""

    # For video playback setting keyint, and min-keyint assuming 15 fps.
    # (GOP : Group of Pictures)
    # https://video.stackexchange.com/a/24684/41475
    GOP_MAX_LENGTH = 60
    GOP_MIN_LENGTH = 30
    if extra_rtsp_args is None:
        extra_rtsp_args = []
    # ffmpeg -hide_banner -loglevel warning -fflags nobuffer \
    # -rtsp_transport tcp \
    # -i 'rtsp://<user>:<pw>@<rtsp_server>/<path>' \
    # -vsync 0 -copyts -codec copy \
    # -segment_format mpegts \
    # -segment_list_flags live \
    # -x264-params keyint=60:min-keyint=30 \
    # -hls_time 2 \
    # -segment_time 2
    # -hls_list_size 0 \
    # -hls_flags discont_start+program_date_time
    # -hls_start_number_source epoch \
    # -hls_list_size 0  \
    # -strftime 1 \
    # -hls_segment_filename 'file_%s.ts' index.m3u8
    ffmpeg_path = _get_ffmpeg_path()
    destination_folder = destination_folder.rstrip("/")
    os.makedirs(destination_folder, exist_ok=True)

    f_args = [
        ffmpeg_path,
        "-hide_banner",
        "-loglevel",
        "warning",
        "-fflags",
        "nobuffer",
        "-rtsp_transport",
        "tcp",
        "-i",
        rtsp_url,
        "-vsync",
        "0",
        "-copyts",
        "-vcodec",
        "copy",
        *extra_rtsp_args,
        "-segment_format",
        "mpegts",
        "-segment_list_flags",
        "live",
        # TODO: test and enable via config if needed
        # f"-x264-params keyint={GOP_MAX_LENGTH}:min-keyint={GOP_MIN_LENGTH}",
        "-hls_time",
        str(HLS_PLAYBACK_SEGMENT_DURATION_SECONDS),
        "-segment_time",
        str(HLS_PLAYBACK_SEGMENT_DURATION_SECONDS),
        "-hls_flags",
        "discont_start+program_date_time",
        "-hls_start_number_source",
        "epoch",
        "-hls_list_size",
        "0",
        "-strftime",
        "1",
        "-hls_segment_filename",
        f"{destination_folder}/%s.ts",
        f"{destination_folder}/{playlist_file_name}",
    ]

    log_file = os.path.join(destination_folder, "hls_stream.log")
    log.debug(
        "Spawning ffmpeg to generate HLS stream for playback.",
        destination_folder=destination_folder,
        log_file=log_file,
    )

    with open(log_file, "wb") as flog:
        return subprocess.Popen(
            f_args,
            stdout=flog,
            stderr=flog,
            cwd=destination_folder,
        )


def rtsp_continuous_fetch(
    rtsp_url: str,
    destination_folder: str,
    output_filename_format: str,
    segment_duration_seconds: int = 30,
    video_format: str = "mp4",
    extra_rtsp_args: typing.Optional[typing.List[str]] = None,
    loglevel: str = "warning",
) -> subprocess.Popen:
    """Return a subprocess handle that continuously fetches videos from given RTSP url and writes the video data to
    unique files under the given destination folder. Each file under destination folder contains video whose duration
    is as specified in the segment duration and file name will be of the form:
    <yyyy>_<MM>_<dd>_<hh>_<mm>_<ss>.<video_format>. This method spawns the subprocess and returns its handle to
    the caller. It is responsibility of the caller to verify if spawned process is running successfully and to wait
    on the process and release any resources held by the process."""
    if extra_rtsp_args is None:
        extra_rtsp_args = []
    output_file_name = f"{output_filename_format}.{video_format}"
    ffmpeg_path = _get_ffmpeg_path()
    os.makedirs(destination_folder, exist_ok=True)

    # sample ffmpeg command
    # "ffmpeg -hide_banner -rtsp_transport tcp -i 'rtsp://admin:<pw>@*************:554/Streaming/Channels/101' -c copy
    # -f segment -strftime 1
    # -segment_time 5 "%Y-%m-%d-%H-%M-%S.mp4"
    f_args = [
        ffmpeg_path,
        "-hide_banner",
        "-loglevel",
        loglevel,
        "-rtsp_transport",
        "tcp",
        "-i",
        rtsp_url,
        "-c",
        "copy",
        *extra_rtsp_args,
        "-f",
        "segment",
        "-reset_timestamps",
        "1",
        "-strftime",
        "1",
        "-segment_time",
        str(segment_duration_seconds),
        output_file_name,
    ]
    log_file = os.path.join(destination_folder, "ffmpeg.log")
    # not logging rtsp_url as it has password.
    log.debug(
        "Spawning ffmpeg to continuously fetch videos.",
        destination_folder=destination_folder,
        log_file=log_file,
        loglevel=loglevel,
    )

    # TODO: Need to move to rotating log file (and also route it via logger ?).
    with open(log_file, "wb") as flog:
        return subprocess.Popen(
            f_args,
            stdout=flog,
            stderr=flog,
            cwd=destination_folder,
        )


def send_audio_stream(
    rtp_url: str,
    audio_file_path: str,
    audio_encoding: str,
    audio_sample_rate: int = 8000,
) -> subprocess.Popen:
    """Return subprocess handle that sends content of given audio file path via RTP to the specified URL."""
    ffmpeg_path = _get_ffmpeg_path()
    f_args = [
        ffmpeg_path,
        "-re",
        "-i",
        audio_file_path,
        "-acodec",
        audio_encoding,
        "-ar",
        str(audio_sample_rate),
        "-f",
        "rtp",
        rtp_url,
    ]
    log.debug(
        "Spawning ffmpeg process to send audio content.", rtp_url=rtp_url
    )
    return subprocess.Popen(f_args)


def dump_image_from_rtsp_camera(
    rtsp_camera_url: str, camera_address: str, file_path: str
) -> bool:
    """Capture a single image from the specified RTSP url and store it in the specified file path. Return True
    if image was captured and successfully written to the given filepath, else False.
    """
    ffmpeg_path = _get_ffmpeg_path()
    f_args = [
        ffmpeg_path,
        "-hide_banner",
        "-loglevel",
        "warning",
        "-y",
        "-skip_frame",
        "nokey",
        "-rtsp_transport",
        "tcp",
        "-i",
        rtsp_camera_url,
        "-frames:v",
        "1",
        file_path,
    ]

    try:
        res = subprocess.run(
            f_args,
            stdout=subprocess.PIPE,
            stderr=subprocess.STDOUT,
            check=True,
            timeout=RTSP_CONNECT_TIMEOUT_SECONDS,
        )
        if res.returncode == 0:
            return True

        log.info(
            "Failed to capture camera image using rtsp.",
            camera=camera_address,
            ret_code=res.returncode,
            error=res.stderr,
            out=res.stdout,
        )
    except TimeoutExpired:
        log.info(
            "Timeout. failed to capture camera image using rtsp.",
            camera=camera_address,
            timeout=RTSP_CONNECT_TIMEOUT_SECONDS,
        )
    except CalledProcessError as cpe:
        log.info(
            "Invocation error. Failed to capture camera image using rtsp.",
            camera=camera_address,
            out=sanitize_rtsp_url(cpe.stdout),
            exc_info=cpe,
            ret_code=cpe.returncode,
        )
    except Exception as exc:  # pylint: disable=broad-except
        log.info(
            "Unknown error. Failed to capture camera image using RTSP.",
            exc_info=exc,
            camera=camera_address,
        )

    return False


def transcode_audio(
    input_filepath: str,
    output_filepath: str,
    encoding_format: str = "pcm_mulaw",
    bitrate: str = "128k",
    sample_rate: str = "16000",
    channel_count: str = "1",
) -> bool:
    """Transcode audio file in given input path to a format suitable
    to be played on AXIS speakers, and write the transcoded data to
    given output filepath. Return True if transcoding was successful
    else False."""
    ffmpeg_path = _get_ffmpeg_path()
    transcode_timeout_seconds = 1
    # ffmpeg -hide_banner -loglevel warning -i <input_filename> -c:a pcm_mulaw
    # -ab 128k -ar 16000 -ac 1 <output_filename>
    f_args = [
        ffmpeg_path,
        "-hide_banner",
        "-loglevel",
        "warning",
        "-i",
        input_filepath,
        "-c:a",
        encoding_format,
        "-ab",
        bitrate,
        "-ar",
        sample_rate,
        "-ac",
        str(channel_count),
        output_filepath,
    ]

    try:
        res = subprocess.run(
            f_args,
            stdout=subprocess.PIPE,
            stderr=subprocess.STDOUT,
            check=True,
            timeout=transcode_timeout_seconds,
        )
        if res.returncode == 0:
            return True

        log.warning(
            "Failed to transcode audio.",
            ret_code=res.returncode,
            error=res.stderr,
            out=res.stdout,
        )
    except TimeoutExpired:
        log.warning(
            "Timeout. failed to transcode audio.",
            timeout=transcode_timeout_seconds,
        )
    except CalledProcessError as cpe:
        log.warning(
            "Invocation error. Failed to transcode audio.",
            error=cpe.stderr,
            out=cpe.stdout,
            ret_code=cpe.returncode,
        )
    except Exception as exc:  # pylint: disable=broad-except
        log.warning(
            "Unknown error. Failed to transcode audio.",
            exc_info=exc,
        )
    return False


def _get_ffmpeg_path() -> str:
    ffmpeg_path = shutil.which("ffmpeg")
    if ffmpeg_path is None:
        raise RuntimeError("Unable to find ffmpeg executable.")
    return ffmpeg_path


def get_frame_from_video(video_path: str, output_path: str) -> bool:
    ffmpeg_path = _get_ffmpeg_path()
    f_args = [
        ffmpeg_path,
        "-hide_banner",
        "-loglevel",
        "warning",
        "-y",
        "-i",
        video_path,
        "-vframes",
        "1",
        "-vsync",
        "0",
        output_path,
    ]
    try:
        res = subprocess.run(
            f_args,
            stdout=subprocess.PIPE,
            stderr=subprocess.STDOUT,
            check=True,
            timeout=RTSP_CONNECT_TIMEOUT_SECONDS,
        )
        if res.returncode == 0:
            return True

        log.warning(
            "Failed to extract video frame",
            ret_code=res.returncode,
            error=res.stderr,
            out=res.stdout,
        )
    except TimeoutExpired:
        log.warning(
            "Timeout. failed to extract video frame",
            timeout=RTSP_CONNECT_TIMEOUT_SECONDS,
        )
    except CalledProcessError as cpe:
        log.warning(
            "Invocation error. Failed to extract video frame",
            out=cpe.stdout,
            error=cpe.stderr,
            ret_code=cpe.returncode,
        )
    except Exception as exc:  # pylint: disable=broad-except
        log.warning(
            "Unknown error. Failed to extract video frame",
            exc_info=exc,
        )

    return False


def is_stream_available(rtsp_url: str):
    """Checks if an RTSP URL provides a stream using ffmpeg.
    Args:
        url: RTSP stream is available or not
    Returns:
        True if the stream seems available, False otherwise.
    """
    # -t 0.1: Set the duration to a very short time (0.1 seconds).
    # -vcodec copy: Copy the video stream without decoding (faster).
    # -an: Disable audio processing (faster).
    # -f null -: Output to null device (don't save the output).
    ffmpeg_path = _get_ffmpeg_path()
    f_args = [
        ffmpeg_path,
        "-hide_banner",
        "-loglevel",
        "warning",
        "-fflags",
        "nobuffer",
        "-rtsp_transport",
        "tcp",
        "-i",
        rtsp_url,
        "-t",
        "0.1",
        "-codec",
        "copy",
        "-an",
        "-f",
        "null",
        "-",
    ]
    try:
        log.debug(
            "start: is_stream_available for rtsp url.", rtsp_url=rtsp_url
        )
        subprocess.run(
            f_args,
            check=True,
            stdout=subprocess.DEVNULL,
            stderr=subprocess.DEVNULL,
        )
        log.debug("end: is_stream_available for rtsp url.", rtsp_url=rtsp_url)
        return True
    except subprocess.CalledProcessError:
        log.warning(
            "not available: is_stream_available for rtsp url.",
            rtsp_url=rtsp_url,
        )
        return False


def combine_image_and_video(
    video_path: str,
    image_path: str,
    output_path: typing.Optional[str] = None,
    image_scale: float = 0.5,
    fps=5,
) -> typing.Tuple[bool, typing.Optional[str]]:
    """
    Overlay an image on the top-left corner of a video using FFmpeg.

    Args:
        video_path (str): Path to input video file
        image_path (str): Path to input image file
        output_path (str): Path for output video file
        image_scale (float): Scale factor for the overlay image (0.2 = 20% of video width)
        fps (int): Output frame rate (default: 5)

    Returns:
        bool: True if successful, False otherwise
        output_path (str): Path for output path if successful, else None
    """
    # Validate input files
    if not os.path.exists(video_path):
        log.debug(f"Error: Video file '{video_path}' not found")
        return False, None

    if not os.path.exists(image_path):
        log.debug(f"Error: Image file '{image_path}' not found")
        return False, None

    if output_path is None:
        output_path = video_path.replace(".mp4", "_overlay.mp4")

    # Create output directory if it doesn't exist
    output_dir = os.path.dirname(output_path)
    if output_dir and not os.path.exists(output_dir):
        os.makedirs(output_dir)

    # FFmpeg command optimized for speed and CPU processing
    cmd = [
        "ffmpeg",
        "-y",
        "-loglevel",
        "error",
        "-hwaccel",
        "cuda",  # Hardware decode only
        "-i",
        video_path,
        "-i",
        image_path,
        "-filter_complex",
        f"[1:v]scale=iw*{image_scale}:ih*{image_scale}[ovr];"  # CPU scaling
        f"[0:v][ovr]overlay=0:0",  # CPU overlay
        "-r",
        str(fps),
        "-c:v",
        "h264_nvenc",  # GPU encoding
        "-preset",
        "p1",
        "-tune",
        "ll",
        "-rc",
        "vbr",
        "-cq",
        "23",
        "-g",
        str(fps * 2),
        "-bf",
        "0",
        "-refs",
        "1",
        "-pix_fmt",
        "yuv420p",
        "-an",
        "-movflags",
        "+faststart",
        output_path,
    ]
    # cmd = [
    #     'ffmpeg',
    #     '-y',
    #     '-loglevel', 'error',
    #     '-hwaccel', 'cuda',           # GPU decoding
    #     '-i', video_path,             # Input video
    #     '-i', image_path,             # Input image (must be correct size already)
    #     '-filter_complex',
    #     '[0:v][1:v]overlay=0:0',      # CPU overlay at top-left, no scaling
    #     '-r', str(fps),
    #     '-c:v', 'h264_nvenc',         # GPU encoding
    #     '-preset', 'p1',
    #     '-tune', 'll',
    #     '-rc', 'vbr',
    #     '-cq', '30',
    #     '-g', str(fps * 2),
    #     '-bf', '0',
    #     '-refs', '1',
    #     '-pix_fmt', 'yuv420p',
    #     '-an',                        # Drop audio
    #     '-movflags', '+faststart',
    #     output_path
    # ]

    try:
        log.debug(f"Processing video: {video_path}")
        log.debug(f"Overlay image: {image_path}")
        log.debug(f"Output: {output_path}")
        log.debug(f"Frame rate: {fps} fps")
        log.debug(f"Image scale: {image_scale * 100}% of video width")
        log.debug("\nRunning FFmpeg...")

        # Run FFmpeg command
        result = subprocess.run(
            cmd, capture_output=True, text=True, check=True
        )

        log.debug("✅ Video processing completed successfully!")
        return True, output_path

    except subprocess.CalledProcessError as e:
        log.debug(f"❌ FFmpeg error: {e}")
        log.debug(f"FFmpeg stderr: {e.stderr}")
        return False, None
    except FileNotFoundError:
        log.debug(
            "❌ FFmpeg not found. Please install FFmpeg and ensure it's in your PATH"
        )
        return False, None
    except Exception as e:
        log.debug(f"❌ Unexpected error: {e}")
        return False, None


def draw_box_on_video(
    video_path: str,
    matched_box: typing.List[float],
    matched_frame: int = 0,
    output_path: typing.Optional[str] = None,
    fps=5,
) -> typing.Tuple[bool, typing.Optional[str]]:
    """
    Overlay an image on the top-left corner of a video using FFmpeg.

    Args:
        video_path (str): Path to input video file
        image_path (str): Path to input image file
        output_path (str): Path for output video file
        image_scale (float): Scale factor for the overlay image (0.2 = 20% of video width)
        fps (int): Output frame rate (default: 5)

    Returns:
        bool: True if successful, False otherwise
        output_path (str): Path for output path if successful, else None
    """
    # Validate input files
    x, y, w, h = matched_box
    if not os.path.exists(video_path):
        log.debug(f"Error: Video file '{video_path}' not found")
        return False, None

    if output_path is None:
        output_path = video_path.replace(".mp4", "_box.mp4")

    # Create output directory if it doesn't exist
    output_dir = os.path.dirname(output_path)
    if output_dir and not os.path.exists(output_dir):
        os.makedirs(output_dir)

    start_time = matched_frame / fps
    end_time = (matched_frame + 5) / fps

    drawbox_filter = (
        f"drawbox=x={x}:y={y}:w={w}:h={h}:color=yellow@1.0:t=3:"
        f"enable='between(t,{start_time:.3f},{end_time:.3f})'"
    )

    # FFmpeg command optimized for speed and CPU processing
    cmd = [
        "ffmpeg",
        "-y",
        "-loglevel",
        "error",
        "-hwaccel",
        "cuda",  # Hardware decode only
        "-i",
        video_path,
        "-vf",
        drawbox_filter,
        "-r",
        str(fps),
        "-c:v",
        "h264_nvenc",  # GPU encoding
        "-preset",
        "p1",
        "-tune",
        "ll",
        "-rc",
        "vbr",
        "-cq",
        "23",
        "-g",
        str(fps * 2),
        "-bf",
        "0",
        "-refs",
        "1",
        "-pix_fmt",
        "yuv420p",
        "-an",
        "-movflags",
        "+faststart",
        output_path,
    ]
    # cmd = [
    #     'ffmpeg',
    #     '-y',
    #     '-loglevel', 'error',
    #     '-hwaccel', 'cuda',           # GPU decoding
    #     '-i', video_path,             # Input video
    #     '-i', image_path,             # Input image (must be correct size already)
    #     '-filter_complex',
    #     '[0:v][1:v]overlay=0:0',      # CPU overlay at top-left, no scaling
    #     '-r', str(fps),
    #     '-c:v', 'h264_nvenc',         # GPU encoding
    #     '-preset', 'p1',
    #     '-tune', 'll',
    #     '-rc', 'vbr',
    #     '-cq', '30',
    #     '-g', str(fps * 2),
    #     '-bf', '0',
    #     '-refs', '1',
    #     '-pix_fmt', 'yuv420p',
    #     '-an',                        # Drop audio
    #     '-movflags', '+faststart',
    #     output_path
    # ]

    try:
        log.debug(f"Processing video: {video_path}")
        log.debug(f"Output: {output_path}")
        log.debug(f"Frame rate: {fps} fps")
        log.debug("\nRunning FFmpeg...")

        # Run FFmpeg command
        result = subprocess.run(
            cmd, capture_output=True, text=True, check=True
        )

        log.debug("✅ Video processing completed successfully!")
        return True, output_path

    except subprocess.CalledProcessError as e:
        log.debug(f"❌ FFmpeg error: {e}")
        log.debug(f"FFmpeg stderr: {e.stderr}")
        return False, None
    except FileNotFoundError:
        log.debug(
            "❌ FFmpeg not found. Please install FFmpeg and ensure it's in your PATH"
        )
        return False, None
    except Exception as e:
        log.debug(f"❌ Unexpected error: {e}")
        return False, None

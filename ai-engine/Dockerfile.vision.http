# Use an official Python runtime as a parent image
FROM python:3.12-slim

# Set environment variables
ENV PYTHONDONTWRITEBYTECODE=1
ENV PYTHONUNBUFFERED=1
ENV PYTHONPATH=/app
RUN mkdir -p /opt/prometheus
ENV PROMETHEUS_MULTIPROC_DIR /opt/prometheus
ENV prometheus_multiproc_dir /opt/prometheus

# Set the working directory in the container
WORKDIR /app

# Copy the current directory contents into the container at /app
COPY vision /app/vision
# Install the dependencies
RUN pip install --upgrade pip
RUN pip install -r vision/requirements-vision.txt

COPY db_controller /app/db_controller
COPY common_utils_v1 /app/common_utils_v1
COPY db_model_rds /app/db_model_rds
COPY controller/alarm_group /app/controller/alarm_group
COPY controller/event /app/controller/event
COPY controller/alarm_group_update /app/controller/alarm_group_update
COPY controller/__init__.py /app/controller/__init__.py
COPY database /app/database
COPY errors /app/errors
COPY models_rds /app/models_rds
COPY common_utils /app/common_utils
COPY interfaces /app/interfaces
COPY config /app/config

# Default command to run HTTP server
CMD gunicorn "vision.services.src.event_processor.http_server:create_app()" --bind=0.0.0.0:8080 --workers=${HTTP_WORKERS:-2} --access-logfile=- --error-logfile=- --preload --timeout=30

# Expose the HTTP port
EXPOSE 8080 

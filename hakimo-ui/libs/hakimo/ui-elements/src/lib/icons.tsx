/* eslint-disable max-lines */
import {
  ChatBubbleLeftEllipsisIcon,
  CheckIcon,
  ClockIcon,
  ExclamationCircleIcon,
  EyeIcon,
  PhoneArrowUpRightIcon,
  SpeakerWaveIcon,
} from '@heroicons/react/24/outline';
import Hakimo<PERSON>ogo from './hakimo-logo/HakimoLogo';

export function DoorIcon() {
  return (
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
      <path fill="none" d="M0 0h24v24H0V0z" />
      <path
        fill="currentColor"
        d="M19 19V5c0-1.1-.9-2-2-2H7c-1.1 0-2 .9-2 2v14H3v2h18v-2h-2zm-2 0H7V5h10v14zm-4-8h2v2h-2v-2z"
      />
    </svg>
  );
}

export function CameraIcon() {
  return (
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
      <path fill="none" d="M0 0h24v24H0V0z" />
      <path
        fill="currentColor"
        d="M15 8v8H5V8h10m1-2H4c-.55 0-1 .45-1 1v10c0 .55.45 1 1 1h12c.55 0 1-.45 1-1v-3.5l4 4v-11l-4 4V7c0-.55-.45-1-1-1z"
      />
    </svg>
  );
}

export function AddCameraIcon(props: React.ComponentProps<'svg'>) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      viewBox="0 0 24 24"
      fill="currentColor"
      {...props}
    >
      <path d="M17 10.5V7c0-.55-.45-1-1-1H4c-.55 0-1 .45-1 1v10c0 .55.45 1 1 1h12c.55 0 1-.45 1-1v-3.5l4 4v-11l-4 4zM14 13h-3v3H9v-3H6v-2h3V8h2v3h3v2z" />
    </svg>
  );
}

export function HourglassIcon() {
  return (
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
      <path fill="none" d="M0 0h24v24H0V0z" />
      <path
        fill="currentColor"
        d="M6 2v6h.01L6 8.01 10 12l-4 4 .01.01H6V22h12v-5.99h-.01L18 16l-4-4 4-3.99-.01-.01H18V2H6zm10 14.5V20H8v-3.5l4-4 4 4zm-4-5-4-4V4h8v3.5l-4 4z"
      />
    </svg>
  );
}

export function SplitViewIcon(props: React.ComponentProps<'svg'>) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      fill="currentColor"
      viewBox="0 0 24 24"
      stroke-width="1.5"
      {...props}
    >
      <path d="M570 936q-24 0-42-18t-18-42V276q0-24 18-42t42-18h210q24 0 42 18t18 42v600q0 24-18 42t-42 18H570Zm-390 0q-24 0-42-18t-18-42V276q0-24 18-42t42-18h210q24 0 42 18t18 42v600q0 24-18 42t-42 18H180Zm0-660v600h210V276H180Zm210 600H180h210Z" />
    </svg>
  );
}

export function LiveViewIcon(props: React.ComponentProps<'svg'>) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      viewBox="0 0 24 24"
      stroke-width="1.5"
      fill="currentColor"
      {...props}
    >
      <path d="M130 626q-20 0-35-15t-15-35q0-20 15-35t35-15q20 0 35 15t15 35q0 20-15 35t-35 15Zm73 269-43-43 173-173 43 43-173 173Zm130-424L161 299l43-43 172 172-43 43Zm147 505q-20 0-35-15t-15-35q0-20 15-35t35-15q20 0 35 15t15 35q0 20-15 35t-35 15Zm0-700q-20 0-35-15t-15-35q0-20 15-35t35-15q20 0 35 15t15 35q0 20-15 35t-35 15Zm147 195-43-43 172-172 43 43-172 172Zm129 423L584 722l43-43 172 172-43 43Zm74-268q-20 0-35-15t-15-35q0-20 15-35t35-15q20 0 35 15t15 35q0 20-15 35t-35 15Z" />
    </svg>
  );
}

export function EventsViewIcon(props: React.ComponentProps<'svg'>) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      viewBox="0 0 24 24"
      stroke-width="1.5"
      fill="currentColor"
      {...props}
    >
      <path d="M197 859q-54-54-85.5-126.5T80 576q0-84 31.5-156.5T197 293l43 43q-46 46-73 107.5T140 576q0 71 26.5 132T240 816l-43 43Zm113-113q-32-32-51-75.5T240 576q0-51 19-94.5t51-75.5l43 43q-24 24-38.5 56.5T300 576q0 38 14 70t39 57l-43 43Zm170-90q-33 0-56.5-23.5T400 576q0-33 23.5-56.5T480 496q33 0 56.5 23.5T560 576q0 33-23.5 56.5T480 656Zm170 90-43-43q24-24 38.5-56.5T660 576q0-38-14-70t-39-57l43-43q32 32 51 75.5t19 94.5q0 50-19 93.5T650 746Zm113 113-43-43q46-46 73-107.5T820 576q0-71-26.5-132T720 336l43-43q54 55 85.5 127.5T880 576q0 83-31.5 155.5T763 859Z" />
    </svg>
  );
}

export function PlaybackSpeedIcon(props: React.ComponentProps<'svg'>) {
  return (
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 -960 960 960" {...props}>
      <path d="M166-234q-38-45-59.5-100.5T81-450h62q5 47 22 91t47 80l-46 45ZM81-530q2-60 24.5-115T166-746l46 45q-29 38-46.5 81T143-530H81ZM441-89q-59-8-114-28t-102-55l46-48q38 26 81 44.5t89 26.5v60ZM273-760l-48-48q48-36 103.5-55.5T444-891v60q-47 8-90 25.5T273-760Zm114 441v-342l268 171-268 171ZM524-89v-60q128-19 212-115.5T820-490q0-129-84-225.5T524-831v-60q154 15 255 130.5T880-490q0 155-101 270T524-89Z" />
    </svg>
  );
}

export function FullScreenIcon(props: React.ComponentProps<'svg'>) {
  return (
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 -960 960 960" {...props}>
      <path d="M200-200v-193h60v133h133v60H200Zm0-367v-193h193v60H260v133h-60Zm367 367v-60h133v-133h60v193H567Zm133-367v-133H567v-60h193v193h-60Z" />
    </svg>
  );
}

export function FullScreenExitIcon(props: React.ComponentProps<'svg'>) {
  return (
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 -960 960 960" {...props}>
      <path d="M333-200v-133H200v-60h193v193h-60Zm234 0v-193h193v60H627v133h-60ZM200-567v-60h133v-133h60v193H200Zm367 0v-193h60v133h133v60H567Z" />
    </svg>
  );
}

export function Forward10Icon(props: React.ComponentProps<'svg'>) {
  return (
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 -960 960 960" {...props}>
      <path d="M360-310v-212h-54v-49h104v261h-50Zm147 0q-18.7 0-31.35-12.65Q463-335.3 463-354v-173q0-18.7 12.65-31.35Q488.3-571 507-571h83q18.7 0 31.35 12.65Q634-545.7 634-527v173q0 18.7-12.65 31.35Q608.7-310 590-310h-83Zm6-50h71v-162h-71v162ZM480-80q-75 0-140.5-28T225-185q-49-49-77-114.5T120-440q0-75 28-140.5T225-695q49-49 114.5-77T480-800h21l-78-78 41-41 147 147-147 147-41-41 74-74h-17q-125.357 0-212.679 87.321Q180-565.357 180-440t87.321 212.679Q354.643-140 480-140t212.679-87.321Q780-314.643 780-440h60q0 75-28 140.5T735-185q-49 49-114.5 77T480-80Z" />
    </svg>
  );
}

export function Replay10Icon(props: React.ComponentProps<'svg'>) {
  return (
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 -960 960 960" {...props}>
      <path d="M480-80q-75 0-140.5-28T225-185q-49-49-77-114.5T120-440h60q0 125 87.321 212.5Q354.643-140 480-140t212.679-87.321Q780-314.643 780-440t-85-212.679Q610-740 485-740h-22l73 73-42 42-147-147 147-147 41 41-78 78h23q75 0 140.5 28T735-695q49 49 77 114.5T840-440q0 75-28 140.5T735-185q-49 49-114.5 77T480-80ZM360-310v-212h-54v-49h104v261h-50Zm147 0q-18.7 0-31.35-12.65Q463-335.3 463-354v-173q0-18.7 12.65-31.35Q488.3-571 507-571h83q18.7 0 31.35 12.65Q634-545.7 634-527v173q0 18.7-12.65 31.35Q608.7-310 590-310h-83Zm6-50h71v-162h-71v162Z" />
    </svg>
  );
}

export function ArrowPointerIcon(props: React.ComponentProps<'svg'>) {
  return (
    <svg
      viewBox="0 0 24 24"
      stroke-width="1.5"
      xmlns="http://www.w3.org/2000/svg"
      fill="none"
      {...props}
    >
      <path
        d="M12.5 14L9.81494 17.0207C9.27003 17.6337 8.25949 17.3662 8.0893 16.5638L5.56782 4.67685C5.37436 3.76482 6.42406 3.1076 7.15999 3.67999L16.8784 11.2388C17.5366 11.7507 17.316 12.796 16.507 12.9983L12.5 14ZM12.5 14L16.5 20"
        strokeWidth="1.5"
        strokeLinecap="round"
      />
    </svg>
  );
}

export function PanHandIcon(props: React.ComponentProps<'svg'>) {
  return (
    <svg viewBox="0 0 16 16" xmlns="http://www.w3.org/2000/svg" {...props}>
      <rect width="16" height="16" id="icon-bound" fill="none" />
      <path d="M8,8L8,1C8,0.448 8.448,0 9,0C9.552,0 10,0.448 10,1L10,8L11,8L11,2C11,1.448 11.448,1 12,1C12.552,1 13,1.448 13,2L13,8L14,8L14,5C14,4.448 14.448,4 15,4C15.552,4 16,4.448 16,5L16,9L16,9.001L16,14C16,14.53 15.789,15.039 15.414,15.414C15.039,15.789 14.53,16 14,16C11.883,16 8.628,16 7,16C6.37,16 5.778,15.704 5.4,15.2C3.88,13.174 0,8 0,8C0,8 0,8 0,8C0.574,7.426 1.482,7.362 2.131,7.848L5,10L5,3C5,2.448 5.448,2 6,2C6.552,2 7,2.448 7,3L7,8L8,8Z" />
    </svg>
  );
}
export function ResetIcon(props: React.ComponentProps<'svg'>) {
  return (
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 -960 960 960" {...props}>
      <path d="M440-80q-50-5-96-24.5T256-156l56-58q29 21 61.5 34t66.5 18v82Zm80 0v-82q104-15 172-93.5T760-438q0-117-81.5-198.5T480-718h-8l64 64-56 56-160-160 160-160 56 58-62 62h6q75 0 140.5 28.5t114 77q48.5 48.5 77 114T840-438q0 137-91 238.5T520-80ZM198-214q-32-42-51.5-88T122-398h82q5 34 18 66.5t34 61.5l-58 56Zm-76-264q6-51 25-98t51-86l58 56q-21 29-34 61.5T204-478h-82Z" />
    </svg>
  );
}

export function SOPIcon(props: React.ComponentProps<'svg'>) {
  return (
    <svg viewBox="0 0 612 612" xmlns="http://www.w3.org/2000/svg" {...props}>
      <g
        transform="matrix(0.09936400502920151, 0, 0, -0.098921999335289, 0.2945280075073313, 611.5023193359375)"
        fill="currentColor"
        stroke="none"
      >
        <path d="M2845 5653 c-354 -32 -692 -130 -995 -289 -146 -77 -359 -220 -370 -249 -5 -12 -13 -24 -19 -26 -17 -6 -1 -49 18 -46 9 1 59 32 111 69 202 140 397 245 572 308 56 20 98 41 98 48 0 9 10 12 28 10 15 -1 86 12 157 30 239 59 340 69 747 76 160 2 222 7 233 16 12 10 19 10 28 1 9 -9 9 -14 -3 -21 -11 -7 -7 -10 17 -10 18 0 36 -6 40 -13 4 -7 54 -23 111 -35 57 -13 150 -38 208 -57 57 -18 107 -29 110 -24 8 14 27 10 21 -4 -4 -10 21 -24 76 -47 45 -18 129 -57 186 -87 81 -42 106 -51 117 -42 11 9 14 8 14 -5 0 -9 11 -21 25 -26 14 -5 25 -14 25 -19 0 -6 33 -33 74 -60 195 -132 448 -369 584 -548 253 -334 422 -716 488 -1107 14 -79 22 -106 33 -106 12 0 12 -3 3 -12 -14 -14 -16 -88 -2 -88 5 0 14 9 20 20 8 15 17 19 31 15 10 -4 19 -4 19 -1 0 3 -7 54 -16 113 -66 454 -257 893 -543 1248 -168 210 -483 495 -529 481 -31 -10 -54 4 -48 28 4 18 -8 30 -81 76 -194 123 -467 243 -670 294 -57 15 -63 15 -91 -1 -25 -14 -34 -15 -56 -5 -30 13 -35 32 -8 32 14 1 15 2 2 9 -19 11 -186 38 -310 51 -89 9 -367 11 -455 3z m-155 -72 c0 -5 -7 -11 -15 -15 -9 -3 -15 0 -15 9 0 8 7 15 15 15 8 0 15 -4 15 -9z m-855 -251 c-3 -5 -10 -10 -16 -10 -5 0 -9 5 -9 10 0 6 7 10 16 10 8 0 12 -4 9 -10z m2835 -240 c0 -5 -5 -10 -11 -10 -5 0 -7 5 -4 10 3 6 8 10 11 10 2 0 4 -4 4 -10z m90 -130 c0 -5 -4 -10 -10 -10 -5 0 -10 5 -10 10 0 6 5 10 10 10 6 0 10 -4 10 -10z m490 -590 c0 -5 -2 -10 -4 -10 -3 0 -8 5 -11 10 -3 6 -1 10 4 10 6 0 11 -4 11 -10z" />
        <path d="M2850 5519 c-129 -11 -330 -46 -347 -60 -6 -6 -16 -8 -22 -5 -12 8 -214 -53 -343 -105 -209 -82 -548 -281 -548 -321 0 -10 4 -18 9 -18 5 0 60 34 122 75 342 227 716 357 1144 396 149 13 433 5 538 -16 25 -5 37 -4 37 4 0 18 38 13 59 -8 10 -10 40 -22 67 -26 66 -10 268 -69 360 -105 41 -17 85 -30 99 -30 13 0 28 -6 32 -14 4 -7 45 -32 90 -55 45 -22 117 -62 160 -87 43 -26 83 -47 90 -47 7 0 10 -4 8 -8 -3 -4 1 -9 8 -12 6 -2 51 -34 100 -71 48 -36 89 -66 92 -66 3 0 5 10 5 23 0 16 -23 39 -90 87 -348 251 -743 408 -1160 460 -124 15 -389 20 -510 9z" />
        <path d="M1418 5005 c-16 -13 -28 -27 -28 -30 0 -13 29 -3 48 18 39 42 24 51 -20 12z" />
        <path d="M1228 4903 c-65 -65 -147 -154 -183 -198 -69 -82 -208 -287 -264 -386 l-31 -57 31 -11 c18 -6 35 -11 40 -11 4 0 24 30 45 68 78 139 261 390 338 461 14 14 26 31 26 39 0 22 41 74 55 69 6 -3 25 6 41 19 24 20 27 25 14 34 -8 5 -23 10 -33 10 -29 0 -14 22 24 35 32 11 46 34 24 42 -6 2 -63 -50 -127 -114z" />
        <path d="M1492 4967 c-22 -23 -30 -57 -14 -57 14 0 88 69 75 70 -6 0 -18 2 -25 5 -8 3 -24 -5 -36 -18z" />
        <path d="M1350 4834 c-45 -45 -45 -45 -15 -38 30 7 104 62 105 77 0 4 -10 7 -23 7 -14 0 -40 -18 -67 -46z" />
        <path d="M4703 4872 c-7 -5 29 -49 95 -118 59 -61 132 -143 162 -182 50 -66 78 -83 87 -57 2 6 -13 28 -32 49 -19 21 -35 41 -35 43 0 15 -223 248 -251 263 -8 5 -20 5 -26 2z" />
        <path d="M1238 4722 c-67 -70 -158 -184 -158 -199 0 -7 -4 -13 -10 -13 -18 0 -152 -217 -225 -365 -142 -288 -223 -587 -232 -857 -3 -87 -9 -158 -14 -158 -13 0 -11 -58 2 -66 8 -4 10 -28 6 -70 -4 -37 -2 -66 3 -69 6 -4 10 -60 10 -125 0 -301 139 -719 353 -1057 34 -55 58 -83 70 -83 13 0 17 -5 13 -18 -11 -34 263 -348 395 -453 32 -25 38 -27 43 -12 5 12 -21 43 -99 116 -117 110 -229 240 -331 384 -75 106 -90 131 -169 285 -382 746 -344 1664 97 2372 56 90 193 273 264 353 33 38 43 56 37 67 -7 13 -17 7 -55 -32z" />
        <path d="M 2925.122 5330.458 C 2723.1 5316.441 2455.137 5260.373 2293.797 5200.101 C 2128.248 5137.024 2000.58 5071.145 2008.998 5051.52 C 2013.207 5040.307 2010.4 5031.896 2001.983 5031.896 C 1993.564 5031.896 1985.148 5038.905 1982.342 5047.315 C 1975.327 5069.741 1698.945 4881.917 1558.65 4757.165 C 1415.55 4631.014 1386.087 4594.569 1373.46 4532.894 C 1366.447 4492.245 1380.475 4503.46 1523.578 4645.031 C 2147.89 5256.168 3023.331 5441.193 3851.071 5134.222 C 4295.806 4970.222 4698.453 4632.415 4956.596 4209.105 C 5022.535 4101.174 5141.785 3847.469 5141.785 3815.228 C 5141.785 3808.218 5151.604 3797.006 5164.232 3792.8 C 5176.858 3787.192 5183.874 3774.579 5178.263 3763.364 C 5174.053 3750.748 5185.277 3677.862 5204.918 3600.768 C 5254.021 3396.123 5273.662 3202.688 5263.841 2992.433 C 5256.826 2831.24 5228.769 2627.994 5203.515 2546.697 C 5196.501 2524.27 5199.305 2520.065 5217.545 2527.07 C 5249.813 2539.689 5245.603 2513.057 5211.933 2494.834 C 5197.903 2486.423 5178.263 2455.586 5169.843 2424.748 C 5148.801 2349.06 5040.772 2092.548 5004.296 2032.275 C 4987.46 2005.643 4973.43 1976.206 4973.43 1966.394 C 4973.43 1957.984 4960.805 1935.558 4945.372 1917.336 C 4931.343 1900.515 4880.836 1834.636 4834.539 1772.962 C 4788.241 1711.289 4734.929 1644.005 4715.288 1622.98 C 4695.648 1601.956 4678.811 1572.522 4678.811 1555.701 C 4678.811 1540.282 4668.991 1527.666 4656.365 1527.666 C 4645.142 1527.666 4583.411 1482.812 4520.279 1428.144 C 4227.06 1174.438 3886.145 1003.432 3517.169 923.537 C 3417.559 902.511 3308.129 892.7 3111.716 887.091 C 2853.574 880.084 2842.349 878.682 2856.38 853.452 C 2869.006 829.623 2888.648 826.818 3055.6 826.818 C 3158.014 826.818 3303.922 836.632 3381.083 847.844 C 3552.243 871.674 3797.758 934.751 3811.788 957.177 C 3817.399 966.989 3823.011 969.794 3823.011 964.186 C 3823.011 948.767 3947.874 999.228 4095.185 1073.518 C 4239.688 1146.404 4462.757 1294.984 4483.802 1332.829 C 4490.818 1348.25 4504.846 1359.463 4514.668 1359.463 C 4534.308 1359.463 4757.378 1583.734 4842.956 1688.861 C 5113.724 2023.866 5290.498 2493.43 5318.557 2953.187 C 5331.183 3150.826 5315.751 3349.866 5287.693 3349.866 C 5272.26 3349.866 5270.857 3355.474 5284.885 3376.497 C 5314.349 3424.156 5262.438 3649.829 5160.024 3921.755 C 5131.963 3991.84 5103.907 4050.712 5098.294 4050.712 C 5091.279 4050.712 5085.667 4061.925 5085.667 4075.943 C 5085.667 4115.191 4925.732 4366.092 4810.689 4509.064 C 4507.651 4880.514 4090.976 5145.433 3622.391 5264.578 C 3545.228 5284.202 3479.289 5296.817 3475.081 5292.611 C 3470.871 5288.407 3461.05 5289.808 3452.633 5296.817 C 3435.798 5310.833 3125.747 5341.673 3041.569 5337.466 C 3012.107 5336.064 2960.198 5333.262 2925.122 5330.458 Z" />
        <path d="M5048 4492 c-25 -5 -23 -13 15 -54 17 -20 33 -42 35 -50 6 -22 19 -30 36 -21 13 7 11 14 -12 51 -15 23 -32 42 -39 42 -6 0 -13 8 -15 18 -2 11 -10 16 -20 14z" />
        <path d="M5135 4340 c-3 -5 5 -27 19 -48 41 -66 136 -261 166 -343 16 -43 33 -86 39 -96 24 -46 111 -427 111 -487 0 -6 7 0 17 13 14 21 15 33 4 105 -15 98 -65 296 -77 305 -5 3 -10 17 -11 31 -2 45 -75 223 -85 208 -5 -8 -7 -4 -3 12 5 20 -1 32 -25 55 -20 19 -30 38 -30 59 0 34 -87 196 -106 196 -7 0 -15 -4 -19 -10z" />
        <path d="M725 4210 c-68 -132 -135 -318 -135 -376 0 -30 -6 -49 -19 -61 -11 -10 -26 -46 -35 -79 -19 -73 -19 -74 -6 -74 6 0 10 14 10 30 0 39 10 38 34 -3 l19 -32 14 50 c34 126 108 340 153 440 28 60 46 113 42 116 -4 4 -19 11 -34 14 -23 6 -29 2 -43 -25z" />
        <path d="M 1234.569 4401.136 C 1132.152 4260.965 1088.662 4175.463 1097.081 4130.609 C 1104.095 4094.164 1109.707 4099.773 1179.854 4214.708 C 1220.541 4281.99 1254.212 4349.272 1255.613 4361.887 C 1255.613 4374.503 1268.24 4408.146 1285.075 4434.776 C 1331.374 4513.272 1304.715 4493.646 1234.569 4401.136 Z" />
        <path d="M 1011.5 4015.67 C 862.788 3682.066 792.64 3317.627 812.28 2967.204 C 826.311 2710.695 864.19 2529.875 956.786 2280.374 C 1001.678 2159.828 1010.097 2144.411 1028.335 2162.633 C 1040.963 2175.248 1043.768 2190.666 1036.753 2201.881 C 1031.143 2213.094 1024.125 2229.913 1022.724 2242.529 C 1021.322 2253.744 1000.277 2319.622 976.426 2388.304 C 815.088 2853.667 830.519 3407.337 1018.515 3890.921 C 1062.005 4000.251 1070.426 4040.901 1060.604 4066.13 C 1049.38 4096.968 1045.17 4094.164 1011.5 4015.67 Z" />
        <path d="M503 3540 c-50 -268 -53 -736 -7 -927 4 -15 11 -20 22 -17 16 5 16 5 0 -8 -14 -12 -15 -20 -5 -64 7 -28 18 -58 26 -67 7 -9 10 -24 7 -33 -9 -24 30 -156 102 -339 41 -106 64 -155 140 -293 46 -82 61 -102 77 -100 16 3 19 -2 17 -24 -5 -50 147 -251 336 -441 105 -106 239 -224 246 -216 2 2 5 20 7 39 3 34 -5 44 -173 210 -112 112 -205 215 -259 288 -66 90 -88 112 -108 113 -25 1 -25 1 -3 7 l22 7 -24 35 c-133 196 -271 530 -327 792 -12 57 -24 110 -27 118 -3 8 -7 31 -8 50 -2 19 -11 94 -20 165 -23 193 -27 256 -16 279 6 12 13 72 17 135 3 63 8 126 11 140 l6 26 -21 -25 -20 -25 -3 120 -3 120 -12 -65z m77 -1140 c0 -5 -4 -10 -10 -10 -5 0 -10 5 -10 10 0 6 5 10 10 10 6 0 10 -4 10 -10z" />
        <path d="M 1680.706 3700.289 C 1596.529 3682.066 1519.369 3638.614 1468.862 3581.145 C 1398.714 3502.652 1379.073 3440.976 1384.685 3326.037 C 1388.896 3240.534 1395.909 3216.705 1432.386 3164.84 C 1491.31 3080.741 1617.574 3019.065 1780.318 2992.433 C 2003.387 2955.99 2060.907 2920.948 2052.491 2824.231 C 2045.474 2735.925 1997.774 2712.096 1833.629 2713.496 C 1691.93 2713.496 1637.217 2730.318 1527.785 2801.804 L 1485.698 2831.24 L 1447.818 2791.993 L 1409.938 2751.343 L 1454.831 2758.351 L 1501.131 2765.361 L 1466.055 2727.515 C 1440.803 2699.481 1423.968 2692.472 1395.909 2698.079 C 1369.254 2703.688 1352.416 2698.079 1334.178 2678.456 C 1310.328 2651.823 1310.328 2650.421 1370.656 2606.969 C 1517.966 2499.038 1641.426 2459.791 1823.809 2461.191 C 1966.909 2462.595 2095.981 2496.235 2145.084 2545.294 C 2156.308 2556.506 2171.74 2564.919 2180.157 2564.919 C 2199.799 2564.919 2265.737 2654.627 2265.737 2681.259 C 2265.737 2693.873 2272.752 2707.891 2279.766 2712.096 C 2288.185 2716.302 2293.797 2735.925 2293.797 2755.548 C 2293.797 2782.18 2286.782 2789.188 2257.321 2789.188 C 2211.023 2789.188 2215.231 2834.043 2262.932 2845.257 C 2281.17 2849.462 2295.201 2856.469 2295.201 2863.478 C 2293.797 2869.087 2292.393 2890.112 2292.393 2912.539 C 2290.99 2934.967 2288.185 2940.57 2283.977 2926.554 C 2275.559 2895.717 2229.26 2892.914 2218.037 2923.751 C 2205.411 2955.99 2206.814 2957.393 2246.097 2957.393 C 2288.185 2957.393 2285.378 2975.614 2234.873 3048.502 C 2163.323 3150.826 2037.056 3211.098 1826.613 3241.935 C 1661.066 3267.166 1620.381 3289.594 1620.381 3356.874 C 1620.381 3391.916 1630.202 3411.54 1659.663 3433.968 C 1693.334 3462 1710.169 3464.805 1816.794 3459.199 C 1900.971 3454.993 1952.879 3443.78 1992.162 3424.156 C 2062.309 3387.712 2084.758 3396.123 2171.74 3487.231 L 2237.679 3557.317 L 2157.71 3607.778 C 2112.815 3635.812 2045.474 3668.05 2006.19 3680.665 C 1917.807 3707.298 1757.869 3717.108 1680.706 3700.289 Z M 2065.115 3571.335 C 2060.907 3560.116 2056.697 3564.327 2056.697 3579.745 C 2055.294 3595.162 2059.504 3603.572 2063.712 3597.966 C 2067.921 3593.761 2069.324 3581.145 2065.115 3571.335 Z M 1684.917 3212.502 C 1676.498 3198.485 1620.381 3194.278 1620.381 3208.293 C 1620.381 3211.098 1631.606 3222.311 1645.634 3232.124 C 1670.886 3250.345 1700.348 3236.33 1684.917 3212.502 Z M 2195.59 2629.395 C 2195.59 2618.182 2189.978 2604.165 2181.562 2599.961 C 2174.545 2595.754 2167.531 2604.165 2167.531 2619.583 C 2167.531 2636.404 2174.545 2649.019 2181.562 2649.019 C 2189.978 2649.019 2195.59 2640.608 2195.59 2629.395 Z M 1828.017 2597.157 C 1821.002 2562.115 1788.733 2557.91 1788.733 2591.549 C 1788.733 2608.372 1797.153 2620.984 1811.181 2620.984 C 1823.809 2620.984 1830.823 2611.174 1828.017 2597.157 Z" />
        <path d="M 2911.094 3693.282 C 2675.399 3627.402 2546.327 3414.343 2546.327 3093.354 C 2546.327 2940.57 2564.566 2842.455 2609.46 2737.327 C 2648.743 2650.421 2773.606 2515.858 2804.471 2527.07 C 2818.5 2532.68 2826.918 2528.473 2826.918 2517.26 C 2826.918 2507.448 2853.574 2492.03 2885.841 2482.217 C 3104.701 2416.337 3324.965 2483.62 3440.005 2653.225 C 3512.96 2762.557 3543.825 2863.478 3552.243 3023.27 C 3564.87 3261.56 3518.571 3421.352 3396.514 3555.915 C 3274.459 3690.476 3089.268 3742.34 2911.094 3693.282 Z M 3194.491 3425.558 C 3223.951 3407.337 3252.011 3391.916 3257.623 3391.916 C 3278.668 3391.916 3309.531 3246.14 3315.144 3122.791 C 3320.754 2982.623 3298.309 2881.7 3240.788 2796.197 L 3204.31 2742.932 L 3236.579 2752.746 C 3254.818 2758.351 3294.1 2765.361 3324.965 2769.568 C 3375.471 2775.172 3378.277 2773.771 3346.009 2759.754 C 3296.906 2737.327 3128.552 2706.489 3047.181 2706.489 C 2936.348 2705.087 2861.99 2762.557 2811.485 2887.309 C 2782.023 2960.197 2784.828 3212.502 2817.097 3295.198 C 2878.826 3456.394 3052.792 3516.668 3194.491 3425.558 Z M 3268.847 3447.985 C 3273.056 3440.976 3270.25 3433.968 3263.234 3433.968 C 3254.818 3433.968 3247.802 3440.976 3247.802 3447.985 C 3247.802 3456.394 3250.608 3462 3253.413 3462 C 3257.623 3462 3264.638 3456.394 3268.847 3447.985 Z M 2748.353 2908.333 C 2752.562 2897.119 2749.755 2887.309 2742.741 2887.309 C 2734.324 2887.309 2728.711 2897.119 2728.711 2908.333 C 2728.711 2919.548 2731.516 2929.358 2734.324 2929.358 C 2737.129 2929.358 2742.741 2919.548 2748.353 2908.333 Z M 2797.455 2674.251 C 2798.858 2660.234 2793.246 2649.019 2787.634 2649.019 C 2782.023 2649.019 2776.411 2660.234 2775.008 2674.251 C 2773.606 2686.865 2769.396 2723.31 2765.186 2754.146 L 2756.77 2810.215 L 2776.411 2754.146 C 2787.634 2723.31 2797.455 2686.865 2797.455 2674.251 Z" />
        <path d="M 3837.041 3295.198 C 3837.041 2984.025 3841.25 2887.309 3855.279 2883.103 C 3869.309 2877.495 3869.309 2874.693 3855.279 2874.693 C 3841.25 2873.292 3837.041 2824.231 3837.041 2677.054 L 3837.041 2480.815 L 3956.293 2480.815 L 4075.543 2480.815 L 4075.543 2706.489 L 4075.543 2933.563 L 4016.619 2923.751 C 3977.336 2916.743 3961.905 2918.145 3970.322 2927.957 C 3992.77 2948.981 4085.363 2961.595 4095.185 2944.775 C 4107.811 2923.751 4429.087 2923.751 4524.485 2944.775 C 4782.63 2999.442 4901.88 3307.815 4750.362 3532.086 C 4694.245 3614.786 4612.874 3666.647 4506.248 3684.87 C 4457.147 3693.282 4285.985 3700.289 4127.452 3700.289 L 3837.041 3700.289 L 3837.041 3295.198 Z M 4482.399 3616.19 C 4482.399 3609.177 4476.788 3602.172 4468.369 3602.172 C 4461.353 3602.172 4454.339 3609.177 4454.339 3616.19 C 4454.339 3624.596 4461.353 3630.204 4468.369 3630.204 C 4476.788 3630.204 4482.399 3624.596 4482.399 3616.19 Z M 4539.92 3407.337 C 4569.382 3376.497 4580.604 3352.67 4580.604 3314.824 C 4580.604 3276.977 4569.382 3253.149 4539.92 3222.311 L 4499.233 3181.661 L 4287.388 3181.661 L 4075.543 3181.661 L 4075.543 3314.824 L 4075.543 3447.985 L 4287.388 3447.985 L 4499.233 3447.985 L 4539.92 3407.337 Z M 4624.097 3340.054 C 4612.874 3319.029 4610.066 3319.029 4608.664 3337.251 C 4608.664 3361.079 4622.694 3382.107 4633.917 3372.292 C 4636.722 3368.087 4632.514 3354.071 4624.097 3340.054 Z M 3925.427 3312.02 C 3915.606 3309.218 3898.77 3309.218 3890.354 3312.02 C 3880.532 3316.225 3887.548 3319.029 3907.189 3319.029 C 3926.83 3319.029 3933.846 3316.225 3925.427 3312.02 Z M 4321.058 3014.862 C 4301.417 2998.041 4103.6 2944.775 4103.6 2957.393 C 4103.6 2962.998 4149.899 2979.821 4206.017 2995.237 C 4328.074 3030.281 4337.895 3031.681 4321.058 3014.862 Z M 3907.189 2901.325 C 3907.189 2894.316 3901.578 2887.309 3894.562 2887.309 C 3886.145 2887.309 3876.324 2894.316 3872.114 2901.325 C 3867.907 2909.735 3873.518 2915.342 3884.742 2915.342 C 3897.369 2915.342 3907.189 2909.735 3907.189 2901.325 Z" />
        <path d="M5483 3226 c4 -55 7 -152 5 -214 -2 -94 -1 -112 12 -110 8 2 17 14 21 28 9 31 9 189 1 299 -6 72 -10 85 -27 91 -20 8 -20 6 -12 -94z" />
        <path d="M5593 3273 c-10 -3 -13 -53 -13 -201 0 -108 -5 -237 -11 -287 -26 -222 -83 -454 -159 -648 -45 -114 -151 -330 -194 -394 -15 -23 -29 -48 -30 -57 -1 -9 -9 -22 -18 -28 -9 -7 -51 -60 -94 -118 -91 -123 -354 -390 -469 -477 -124 -93 -226 -159 -350 -227 -63 -35 -111 -67 -107 -71 4 -5 -1 -5 -11 -2 -23 9 -57 -9 -57 -28 0 -8 -8 -16 -17 -16 -55 -4 -80 -11 -203 -52 -74 -25 -161 -51 -194 -58 -48 -11 -56 -15 -42 -24 24 -18 19 -25 -19 -25 -19 0 -35 4 -35 10 0 5 -18 7 -42 5 -24 -3 -96 -12 -161 -20 -65 -8 -144 -16 -175 -16 -31 -1 -49 -3 -39 -6 19 -5 21 -12 8 -32 -8 -12 -12 -12 -25 -2 -8 7 -12 19 -9 27 7 18 -57 20 -75 2 -10 -10 -17 -10 -32 0 -11 6 -48 12 -83 12 -189 0 -561 76 -773 157 -11 4 -14 -4 -14 -35 0 -39 1 -40 57 -61 76 -30 295 -86 408 -105 243 -41 597 -46 820 -11 459 72 862 243 1217 517 68 53 76 62 63 75 -22 21 -7 38 16 17 17 -16 24 -11 145 107 144 141 240 256 346 414 92 136 192 321 184 340 -3 8 0 14 6 12 18 -3 22 35 4 45 -11 6 -14 17 -9 41 7 38 16 45 47 40 16 -3 25 1 29 14 3 10 19 56 36 103 61 170 111 400 131 605 11 113 13 386 4 434 -6 31 -26 39 -61 24z m-353 -1583 c0 -5 -2 -10 -4 -10 -3 0 -8 5 -11 10 -3 6 -1 10 4 10 6 0 11 -4 11 -10z m-1520 -1110 c0 -5 -2 -10 -4 -10 -3 0 -8 5 -11 10 -3 6 -1 10 4 10 6 0 11 -4 11 -10z" />
        <path d="M5496 2837 c-11 -8 -20 -36 -27 -87 -8 -60 -29 -176 -53 -295 -20 -93 -117 -348 -186 -484 -29 -58 -54 -123 -56 -145 -4 -39 -3 -40 11 -21 9 11 49 88 90 170 72 146 121 274 163 422 19 68 19 74 4 83 -15 9 -15 10 4 16 19 5 24 21 43 132 26 160 34 222 29 222 -2 0 -12 -6 -22 -13z" />
        <path d="M 1087.26 1994.428 C 1171.438 1836.038 1344 1611.768 1491.31 1468.795 C 1618.978 1342.643 1648.439 1325.822 1648.439 1372.078 C 1648.439 1405.718 1613.366 1435.155 1585.307 1425.341 C 1574.084 1419.735 1561.457 1422.537 1557.247 1429.547 C 1551.635 1437.958 1555.846 1443.565 1564.263 1443.565 C 1574.084 1443.565 1539.009 1489.82 1487.1 1545.887 C 1421.162 1615.973 1384.685 1645.409 1365.044 1642.605 C 1342.595 1638.399 1338.388 1644.005 1345.403 1665.032 C 1352.416 1686.057 1334.178 1719.697 1283.673 1788.383 C 1244.39 1841.644 1198.093 1907.524 1182.661 1936.959 C 1167.228 1964.994 1140.572 1991.625 1125.138 1995.831 C 1108.306 2000.038 1101.289 2009.847 1105.498 2022.464 C 1109.707 2035.078 1095.678 2056.105 1070.426 2074.326 L 1026.934 2106.565 L 1087.26 1994.428 Z" />
        <path d="M 1663.872 1341.24 C 1651.245 1325.822 1670.886 1304.796 1757.869 1243.122 C 1895.358 1145.004 2066.519 1046.886 2083.353 1058.098 C 2114.219 1077.722 2091.771 1102.953 2000.58 1153.415 C 1947.268 1181.448 1856.076 1240.317 1795.748 1282.371 C 1736.825 1324.421 1686.319 1359.463 1684.917 1359.463 C 1682.11 1359.463 1672.29 1351.052 1663.872 1341.24 Z" />
        <path d="M5067 1693 c-56 -81 -159 -212 -196 -250 -27 -28 -26 -33 3 -33 19 0 43 23 106 101 141 175 189 259 150 259 -4 0 -33 -35 -63 -77z" />
        <path d="M 2111.413 1074.92 C 2112.815 1031.467 2163.323 1003.432 2355.526 938.955 C 2434.091 913.724 2453.733 910.92 2463.553 926.34 C 2478.987 951.569 2469.165 958.577 2377.973 988.013 C 2335.885 1002.031 2258.723 1031.467 2206.814 1052.492 C 2145.084 1077.722 2111.413 1086.132 2111.413 1074.92 Z" />
        <path d="M 2494.418 930.546 C 2490.208 919.331 2490.208 906.717 2494.418 901.111 C 2505.643 889.896 2772.203 836.632 2779.218 845.04 C 2793.246 859.058 2765.186 896.903 2737.129 902.511 C 2721.696 906.717 2661.37 917.929 2605.251 929.143 C 2515.462 947.365 2501.433 947.365 2494.418 930.546 Z" />
        <path d="M4649 1223 c-35 -30 -95 -77 -132 -106 -61 -46 -65 -52 -46 -59 28 -11 105 44 130 94 15 30 25 38 47 38 19 0 39 11 60 33 36 37 38 43 19 50 -8 3 -42 -20 -78 -50z" />
        <path d="M1574 1115 c-9 -22 0 -30 119 -109 152 -101 341 -199 367 -191 28 9 61 -4 58 -22 -2 -11 13 -21 50 -34 50 -17 53 -17 50 -1 -2 9 -13 18 -26 20 -12 2 -22 8 -22 14 0 6 -3 9 -6 5 -3 -3 -11 -1 -18 4 -6 5 -78 43 -161 83 -82 41 -208 112 -278 160 -95 64 -129 82 -133 71z" />
        <path d="M1540 1030 c0 -5 -10 -20 -21 -31 -12 -12 -17 -24 -13 -28 90 -66 289 -187 393 -239 l133 -66 19 25 c14 18 21 21 27 12 7 -10 12 -10 25 0 15 10 7 16 -60 46 -120 52 -237 117 -363 200 -63 42 -121 80 -127 84 -8 5 -13 4 -13 -3z" />
        <path d="M4270 955 c-52 -30 -99 -56 -103 -58 -5 -1 -7 -13 -5 -25 3 -22 8 -20 117 42 132 76 130 74 111 86 -21 13 -14 16 -120 -45z" />
        <path d="M3998 821 c-83 -34 -102 -51 -77 -66 18 -12 190 66 187 83 -5 24 -17 22 -110 -17z" />
        <path d="M3776 739 c-185 -59 -395 -94 -618 -105 -128 -7 -138 -9 -138 -27 0 -18 6 -19 123 -13 227 12 345 29 363 54 10 13 15 14 27 4 13 -11 31 -9 99 7 121 28 285 83 268 89 -8 2 -19 8 -25 12 -5 4 -50 -6 -99 -21z" />
        <path d="M2271 736 c-23 -27 350 -116 549 -131 47 -3 104 -8 128 -11 23 -3 42 -2 42 3 -1 25 -25 33 -135 43 -147 14 -351 51 -465 85 -99 29 -104 29 -119 11z" />
      </g>
    </svg>
  );
}

export function ContactsIcon(props: React.ComponentProps<'svg'>) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      height="24"
      viewBox="0 -960 960 960"
      width="24"
      fill="currentColor"
      {...props}
    >
      <path d="M160-40v-80h640v80H160Zm0-800v-80h640v80H160Zm320 400q50 0 85-35t35-85q0-50-35-85t-85-35q-50 0-85 35t-35 85q0 50 35 85t85 35ZM160-160q-33 0-56.5-23.5T80-240v-480q0-33 23.5-56.5T160-800h640q33 0 56.5 23.5T880-720v480q0 33-23.5 56.5T800-160H160Zm70-80q45-56 109-88t141-32q77 0 141 32t109 88h70v-480H160v480h70Zm118 0h264q-29-20-62.5-30T480-280q-36 0-69.5 10T348-240Zm132-280q-17 0-28.5-11.5T440-560q0-17 11.5-28.5T480-600q17 0 28.5 11.5T520-560q0 17-11.5 28.5T480-520Zm0 40Z" />
    </svg>
  );
}

export function FloorPlanIcon(props: React.ComponentProps<'svg'>) {
  return (
    <svg
      viewBox="0 0 122.88 110.81"
      fill="currentColor"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <path
        d="M 86.276 72.172 C 84.849 72.172 83.957 70.62 84.671 69.378 C 85.002 68.802 85.614 68.446 86.276 68.446 L 89.829 68.446 L 89.829 54.393 L 86.388 54.393 C 84.96 54.393 84.068 52.841 84.782 51.599 C 85.113 51.023 85.725 50.668 86.388 50.668 L 89.829 50.668 L 89.829 30.937 L 75.277 30.937 L 75.277 50.705 L 78.726 50.705 C 80.153 50.705 81.045 52.257 80.332 53.499 C 80.001 54.076 79.388 54.431 78.726 54.431 L 75.277 54.431 L 75.277 58.29 C 75.277 59.724 73.732 60.621 72.496 59.904 C 71.922 59.571 71.569 58.956 71.569 58.29 L 71.569 30.937 L 46.715 30.937 L 46.715 44.223 L 58.893 44.223 C 59.912 44.218 60.74 45.047 60.74 46.07 L 60.74 68.461 L 71.524 68.461 L 71.524 65.004 C 71.524 63.57 73.069 62.674 74.306 63.391 C 74.879 63.724 75.233 64.338 75.233 65.004 L 75.233 68.461 L 79.601 68.461 C 81.029 68.461 81.921 70.014 81.207 71.256 C 80.876 71.832 80.264 72.187 79.601 72.187 L 44.868 72.187 C 43.848 72.187 43.021 71.356 43.021 70.332 L 43.021 29.059 C 43.025 28.036 43.85 27.208 44.868 27.204 L 91.713 27.204 C 92.717 27.228 93.519 28.051 93.523 29.059 L 93.523 70.317 C 93.523 71.327 92.718 72.152 91.713 72.172 L 86.276 72.172 Z M 19.999 64.974 L 20.207 64.825 C 20.585 64.489 20.987 64.18 21.408 63.901 C 22.266 63.336 23.19 62.88 24.16 62.545 C 25.012 62.248 25.885 62.014 26.771 61.845 L 26.771 7.525 C 19.903 8.583 19.947 15.513 19.984 21.966 C 20.162 30.907 19.584 54.468 19.984 64.974 L 19.999 64.974 Z M 30.383 63.439 C 30.354 63.837 30.201 64.215 29.945 64.52 C 29.66 64.852 29.265 65.069 28.833 65.131 C 27.829 65.274 26.838 65.49 25.866 65.779 C 25.003 66.031 24.181 66.407 23.426 66.897 C 22.697 67.39 22.055 68.002 21.527 68.707 C 20.887 69.576 20.363 70.526 19.969 71.531 C 19.898 72.9 20.001 74.272 20.274 75.615 C 20.506 76.775 20.968 77.877 21.631 78.856 C 22.317 79.816 23.212 80.607 24.249 81.166 C 25.561 81.866 26.984 82.335 28.454 82.552 L 103.513 82.552 L 103.513 17.883 L 30.42 17.883 L 30.42 63.439 L 30.383 63.439 Z M 30.383 14.261 L 104.552 14.261 C 104.895 14.263 105.235 14.331 105.553 14.462 C 106.199 14.734 106.714 15.251 106.984 15.9 C 107.115 16.222 107.183 16.566 107.185 16.914 L 107.185 83.669 C 107.183 84.014 107.115 84.356 106.984 84.675 C 106.849 84.994 106.655 85.285 106.413 85.532 C 106.167 85.775 105.878 85.97 105.56 86.106 C 105.243 86.24 104.903 86.309 104.559 86.307 L 28.358 86.307 C 26.491 86.209 24.673 85.668 23.055 84.727 C 21.334 83.828 19.834 82.556 18.664 81.002 C 17.736 79.653 17.081 78.134 16.736 76.531 C 16.372 74.768 16.242 72.964 16.35 71.166 C 16.35 55.153 15.697 37.8 16.35 21.988 C 16.35 17.741 16.298 13.322 17.774 9.887 C 19.25 6.452 22.328 3.844 28.402 3.725 L 28.595 3.725 C 29.603 3.725 30.42 4.546 30.42 5.558 L 30.42 14.239 L 30.383 14.261 Z M 46.7 47.948 L 46.7 68.461 L 57.046 68.461 L 57.046 47.926 L 46.7 47.948 Z"
        transform="matrix(1, 0, 0, 1, 8.881784197001252e-16, 8.881784197001252e-16)"
      />
    </svg>
  );
}

export function DragIcon(props: React.ComponentProps<'svg'>) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      viewBox="0 0 8 12"
      fill="currentColor"
      {...props}
    >
      <rect width="2" height="2" rx="1"></rect>
      <rect width="2" height="2" rx="1" y="5"></rect>
      <rect width="2" height="2" rx="1" y="10"></rect>
      <rect width="2" height="2" rx="1" x="6"></rect>
      <rect width="2" height="2" rx="1" x="6" y="5"></rect>
      <rect width="2" height="2" rx="1" x="6" y="10"></rect>
    </svg>
  );
}

export function CallingIcon(props: React.ComponentProps<'svg'>) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      viewBox="0 -960 960 960"
      fill="#5f6368"
      height="24"
      width="24"
      {...props}
    >
      <path d="M660-521q-17 0-28.5-11.5T620-561q0-17 11.5-28.5T660-601q17 0 28.5 11.5T700-561q0 17-11.5 28.5T660-521Zm-100-98-42-42q29-29 65.5-44.5T660-721q40 0 76.5 15.5T802-661l-42 42q-20-20-45.5-31T660-661q-29 0-54.5 11T560-619Zm-84-86-42-42q45-45 103.5-69.5T660-841q64 0 122.5 24.5T886-747l-42 42q-37-37-84.5-56.5T660-781q-52 0-99 20t-85 56Zm322 585q-125 0-247-54.5T329-329Q229-429 174.5-551T120-798q0-18 12-30t30-12h162q14 0 25 9.5t13 22.5l26 140q2 16-1 27t-11 19l-97 98q20 37 47.5 71.5T387-386q31 31 65 57.5t72 48.5l94-94q9-9 23.5-13.5T670-390l138 28q14 4 23 14.5t9 23.5v162q0 18-12 30t-30 12ZM241-600l66-66-17-94h-89q5 41 14 81t26 79Zm358 358q39 17 79.5 27t81.5 13v-88l-94-19-67 67ZM241-600Zm358 358Z" />
    </svg>
  );
}

export function CallEndIcon(props: React.ComponentProps<'svg'>) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      viewBox="0 -960 960 960"
      fill="#5f6368"
      {...props}
    >
      <path d="m136-304-92-90q-12-12-12-28t12-28q88-95 203-142.5T480-640q118 0 232.5 47.5T916-450q12 12 12 28t-12 28l-92 90q-11 11-25.5 12t-26.5-8l-116-88q-8-6-12-14t-4-18v-114q-38-12-78-19t-82-7q-42 0-82 7t-78 19v114q0 10-4 18t-12 14l-116 88q-12 9-26.5 8T136-304Zm104-198q-29 15-56 34.5T128-424l40 40 72-56v-62Zm480 2v60l72 56 40-38q-29-26-56-45t-56-33Zm-480-2Zm480 2Z" />
    </svg>
  );
}

export function DialpadIcon(props: React.ComponentProps<'svg'>) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      viewBox="0 -960 960 960"
      fill="#5f6368"
      {...props}
    >
      <path d="M480-40q-33 0-56.5-23.5T400-120q0-33 23.5-56.5T480-200q33 0 56.5 23.5T560-120q0 33-23.5 56.5T480-40ZM240-760q-33 0-56.5-23.5T160-840q0-33 23.5-56.5T240-920q33 0 56.5 23.5T320-840q0 33-23.5 56.5T240-760Zm0 240q-33 0-56.5-23.5T160-600q0-33 23.5-56.5T240-680q33 0 56.5 23.5T320-600q0 33-23.5 56.5T240-520Zm0 240q-33 0-56.5-23.5T160-360q0-33 23.5-56.5T240-440q33 0 56.5 23.5T320-360q0 33-23.5 56.5T240-280Zm480-480q-33 0-56.5-23.5T640-840q0-33 23.5-56.5T720-920q33 0 56.5 23.5T800-840q0 33-23.5 56.5T720-760ZM480-280q-33 0-56.5-23.5T400-360q0-33 23.5-56.5T480-440q33 0 56.5 23.5T560-360q0 33-23.5 56.5T480-280Zm240 0q-33 0-56.5-23.5T640-360q0-33 23.5-56.5T720-440q33 0 56.5 23.5T800-360q0 33-23.5 56.5T720-280Zm0-240q-33 0-56.5-23.5T640-600q0-33 23.5-56.5T720-680q33 0 56.5 23.5T800-600q0 33-23.5 56.5T720-520Zm-240 0q-33 0-56.5-23.5T400-600q0-33 23.5-56.5T480-680q33 0 56.5 23.5T560-600q0 33-23.5 56.5T480-520Zm0-240q-33 0-56.5-23.5T400-840q0-33 23.5-56.5T480-920q33 0 56.5 23.5T560-840q0 33-23.5 56.5T480-760Z" />
    </svg>
  );
}

export function MultiLiveViewIcon(props: React.ComponentProps<'svg'>) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      viewBox="0 0 64 80"
      x="0px"
      y="0px"
      fill="currentColor"
      {...props}
    >
      <g>
        <path d="M59,2H5A3,3,0,0,0,2,5V35a3,3,0,0,0,3,3H28v2.764l-2.895,5.789A1,1,0,0,0,26,48H38a1,1,0,0,0,.9-1.447L36,40.764V38H59a3,3,0,0,0,3-3V5A3,3,0,0,0,59,2ZM34.1,41.447,36.382,46H27.618L29.9,41.447A1,1,0,0,0,30,41V38h4v3A1,1,0,0,0,34.1,41.447ZM60,35a1,1,0,0,1-1,1H5a1,1,0,0,1-1-1V5A1,1,0,0,1,5,4H59a1,1,0,0,1,1,1Z" />
        <path d="M58,5H6A1,1,0,0,0,5,6V34a1,1,0,0,0,1,1H58a1,1,0,0,0,1-1V6A1,1,0,0,0,58,5Zm-1,7H46V7H57ZM33,12V7H44v5Zm11,2v5H33V14ZM31,12H20V7H31Zm0,2v5H20V14ZM18,19H7V14H18Zm0,2v5H7V21Zm2,0H31v5H20Zm11,7v5H20V28Zm2,0H44v5H33Zm0-2V21H44v5Zm13-5H57v5H46Zm0-2V14H57v5ZM18,7v5H7V7ZM7,28H18v5H7Zm39,5V28H57v5Z" />
        <path d="M43.929,50.629A1,1,0,0,0,43,50H21a1,1,0,0,0-.929.629l-4,10A1,1,0,0,0,17,62H47a1,1,0,0,0,.929-1.371ZM18.477,60l3.2-8H42.323l3.2,8Z" />
        <rect x="24" y="53" width="2" height="2" />
        <rect x="22" y="56" width="2" height="2" />
        <rect x="27" y="53" width="2" height="2" />
        <rect x="25" y="56" width="2" height="2" />
        <rect x="28" y="56" width="2" height="2" />
        <polygon points="39.168 52.445 37.465 55 36 55 36 57 40 57 40 55 39.868 55 40.832 53.555 39.168 52.445" />
      </g>
    </svg>
  );
}

export function CollapseIcon() {
  return (
    <svg
      viewBox="0 0 32 32"
      fill="currentColor"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path d="M 12.341 14.977 L 27.684 14.977 C 28.144 14.977 28.451 15.285 28.451 15.749 C 28.451 16.211 28.144 16.521 27.684 16.521 L 12.341 16.521 C 11.881 16.521 11.575 16.211 11.575 15.749 C 11.575 15.285 11.881 14.977 12.341 14.977 Z M 4.67 5.72 L 27.684 5.72 C 28.144 5.72 28.451 6.027 28.451 6.492 C 28.451 6.955 28.144 7.263 27.684 7.263 L 4.67 7.263 C 4.209 7.263 3.903 6.955 3.903 6.492 C 3.903 6.027 4.209 5.72 4.67 5.72 Z M 4.67 24.235 L 27.684 24.235 C 28.144 24.235 28.451 24.543 28.451 25.006 C 28.451 25.469 28.144 25.778 27.684 25.778 L 4.67 25.778 C 4.209 25.778 3.903 25.469 3.903 25.006 C 3.903 24.543 4.209 24.235 4.67 24.235 Z M 9.811 20.145 C 10.117 19.837 10.117 19.376 9.811 19.065 L 6.511 15.749 L 9.811 12.431 C 10.117 12.123 10.117 11.66 9.811 11.352 C 9.503 11.042 9.043 11.042 8.736 11.352 L 4.363 15.749 L 8.736 20.145 C 8.89 20.301 9.043 20.377 9.273 20.377 C 9.503 20.377 9.657 20.301 9.811 20.145 Z" />
    </svg>
  );
}

export function BroadcastIcon(props: React.ComponentProps<'svg'>) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      height="16"
      viewBox="0 0 16 16"
      width="16"
      focusable="false"
      aria-hidden="true"
      {...props}
      // style="pointer-events: none; display: inherit; width: 100%; height: 100%;"
    >
      <path d="M9 8c0 .55-.45 1-1 1s-1-.45-1-1 .45-1 1-1 1 .45 1 1Zm1.11 2.13.71.71C11.55 10.11 12 9.11 12 8c0-1.11-.45-2.11-1.18-2.84l-.71.71c.55.55.89 1.3.89 2.13 0 .83-.34 1.58-.89 2.13Zm-4.93.71.71-.71C5.34 9.58 5 8.83 5 8c0-.83.34-1.58.89-2.13l-.71-.71C4.45 5.89 4 6.89 4 8c0 1.11.45 2.11 1.18 2.84Zm7.05 1.41.71.71C14.21 11.69 15 9.94 15 8s-.79-3.69-2.06-4.96l-.71.71C13.32 4.84 14 6.34 14 8c0 1.66-.68 3.16-1.77 4.25Zm-9.17.71.71-.71C2.68 11.16 2 9.66 2 8c0-1.66.68-3.16 1.77-4.25l-.71-.71C1.79 4.31 1 6.06 1 8s.79 3.69 2.06 4.96Z"></path>
    </svg>
  );
}

export function RedShieldIcon(props: React.ComponentProps<'svg'>) {
  return (
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100" {...props}>
      <defs>
        <linearGradient id="shieldGradient" x1="0%" y1="0%" x2="100%" y2="100%">
          <stop offset="0%" stopColor="#8B0000" />
          <stop offset="100%" stopColor="#5D0E0E" />
        </linearGradient>
      </defs>

      <path
        d="M50,5 L90,20 C90,20 90,60 50,95 C10,60 10,20 10,20 L50,5 Z"
        fill="inherit"
        strokeWidth="2"
      />

      <rect x="45" y="25" width="10" height="35" rx="2" fill="white" />
      <circle cx="50" cy="70" r="5" fill="white" />
    </svg>
  );
}
export const hakimoTimelineIcon = (
  <span className="flex h-8 w-8 items-center justify-center rounded-full">
    <HakimoLogo />
  </span>
);

export const checkTimelineIcon = (
  <span className="flex h-8 w-8 items-center justify-center rounded-full bg-green-500">
    <CheckIcon className="w-5 text-white" aria-hidden="true" />
  </span>
);

export const inProgressTimelineIcon = (
  <span className="flex h-8 w-8 items-center justify-center rounded-full bg-yellow-500">
    <ClockIcon className="w-5 text-white" aria-hidden="true" />
  </span>
);

export const pendingTimelineIcon = (
  <span className="flex h-8 w-8 items-center justify-center rounded-full bg-red-500">
    <ExclamationCircleIcon className="w-5 text-white" aria-hidden="true" />
  </span>
);

export const speakerTalkdownTimelineIcon = (
  <span className="flex h-8 w-8 items-center justify-center rounded-full bg-blue-500">
    <SpeakerWaveIcon className="w-5 text-white" aria-hidden="true" />
  </span>
);

export const callMadeIcon = (
  <span className="flex h-8 w-8 items-center justify-center rounded-full bg-blue-500">
    <PhoneArrowUpRightIcon className="w-5 text-white" aria-hidden="true" />
  </span>
);

export const commentTimelineIcon = (
  <span className="flex h-8 w-8 items-center justify-center rounded-full bg-blue-500">
    <ChatBubbleLeftEllipsisIcon className="w-5 text-white" aria-hidden="true" />
  </span>
);

export const viewTimelineIcon = (
  <span className="flex h-8 w-8 items-center justify-center rounded-full bg-yellow-500">
    <EyeIcon className="w-5 text-white" aria-hidden="true" />
  </span>
);

export function ChevronDownUpIcon(props: React.ComponentProps<'svg'>) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      fill="none"
      viewBox="0 0 24 24"
      strokeWidth="1.5"
      stroke="currentColor"
      aria-hidden="true"
      {...props}
    >
      <path
        strokeLinecap="round"
        strokeLinejoin="round"
        d="M8.25 5.25L12 9 15.75 5.25M8.25 18.75L12 15 15.75 18.75"
      ></path>
    </svg>
  );
}

//Monitoring
export function DesktopFrameIcon(props: React.ComponentProps<'svg'>) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      viewBox="0 0 24 24"
      strokeWidth="1.5"
      fill="none"
      stroke="currentColor"
      {...props}
    >
      <path d="M9 10L11 12L15 8" strokeLinecap="round" strokeLinejoin="round" />
      <path
        d="M20 3H4C2.89543 3 2 3.89543 2 5V15C2 16.1046 2.89543 17 4 17H20C21.1046 17 22 16.1046 22 15V5C22 3.89543 21.1046 3 20 3Z"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path d="M12 17V21" strokeLinecap="round" strokeLinejoin="round" />
      <path d="M8 21H16" strokeLinecap="round" strokeLinejoin="round" />
    </svg>
  );
}

//Door icon
export function DoorsIcon(props: React.ComponentProps<'svg'>) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      viewBox="0 0 24 24"
      strokeWidth="1.5"
      fill="none"
      stroke="currentColor"
      {...props}
    >
      <path d="M11 20H2" strokeLinecap="round" strokeLinejoin="round" />
      <path
        d="M11 4.56201V20.719C11 20.8709 11.0347 21.0208 11.1013 21.1573C11.1679 21.2938 11.2648 21.4134 11.3845 21.5069C11.5042 21.6004 11.6436 21.6653 11.7922 21.6969C11.9408 21.7285 12.0946 21.7258 12.242 21.689L19 20V5.56201C18.9999 5.11602 18.8508 4.68285 18.5763 4.33134C18.3018 3.97984 17.9177 3.73017 17.485 3.62201L13.485 2.62201C13.1902 2.54834 12.8826 2.54278 12.5854 2.60577C12.2882 2.66875 12.0092 2.79863 11.7697 2.98553C11.5301 3.17243 11.3363 3.41144 11.203 3.68443C11.0696 3.95741 11.0002 4.2582 11 4.56201Z"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M11 4H8C7.46957 4 6.96086 4.21071 6.58579 4.58579C6.21071 4.96086 6 5.46957 6 6V20"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path d="M14 12H14.01" strokeLinecap="round" strokeLinejoin="round" />
      <path d="M22 20H19" strokeLinecap="round" strokeLinejoin="round" />
    </svg>
  );
}

//Location icon
export function LocationIcon(props: React.ComponentProps<'svg'>) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      viewBox="0 0 24 24"
      strokeWidth="1.5"
      fill="none"
      stroke="currentColor"
      {...props}
    >
      <path
        d="M20 10C20 14.993 14.461 20.193 12.601 21.799C12.4277 21.9293 12.2168 21.9998 12 21.9998C11.7832 21.9998 11.5723 21.9293 11.399 21.799C9.539 20.193 4 14.993 4 10C4 7.87827 4.84285 5.84344 6.34315 4.34315C7.84344 2.84285 9.87827 2 12 2C14.1217 2 16.1566 2.84285 17.6569 4.34315C19.1571 5.84344 20 7.87827 20 10Z"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M12 13C13.6569 13 15 11.6569 15 10C15 8.34315 13.6569 7 12 7C10.3431 7 9 8.34315 9 10C9 11.6569 10.3431 13 12 13Z"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
}

// Door Group Icon
export function DoorGrpIcon(props: React.ComponentProps<'svg'>) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      viewBox="0 0 24 24"
      strokeWidth="1.5"
      fill="none"
      stroke="currentColor"
      {...props}
    >
      <path
        d="M15.536 11.293C15.3485 11.4805 15.2432 11.7349 15.2432 12C15.2432 12.2652 15.3485 12.5195 15.536 12.707L17.912 15.084C18.0995 15.2715 18.3538 15.3768 18.619 15.3768C18.8842 15.3768 19.1385 15.2715 19.326 15.084L21.703 12.707C21.8905 12.5195 21.9958 12.2652 21.9958 12C21.9958 11.7349 21.8905 11.4805 21.703 11.293L19.326 8.91602C19.1385 8.72855 18.8842 8.62323 18.619 8.62323C18.3538 8.62323 18.0995 8.72855 17.912 8.91602L15.536 11.293Z"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M2.297 11.293C2.10953 11.4805 2.00421 11.7349 2.00421 12C2.00421 12.2652 2.10953 12.5195 2.297 12.707L4.674 15.084C4.86153 15.2715 5.11583 15.3768 5.381 15.3768C5.64616 15.3768 5.90047 15.2715 6.088 15.084L8.465 12.707C8.65247 12.5195 8.75778 12.2652 8.75778 12C8.75778 11.7349 8.65247 11.4805 8.465 11.293L6.088 8.91602C5.90047 8.72855 5.64616 8.62323 5.381 8.62323C5.11583 8.62323 4.86153 8.72855 4.674 8.91602L2.297 11.293Z"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M8.91603 17.912C8.82305 18.0049 8.74929 18.1152 8.69897 18.2366C8.64864 18.358 8.62274 18.4881 8.62274 18.6195C8.62274 18.7509 8.64864 18.8811 8.69897 19.0025C8.74929 19.1238 8.82305 19.2341 8.91603 19.327L11.293 21.703C11.4806 21.8905 11.7349 21.9958 12 21.9958C12.2652 21.9958 12.5195 21.8905 12.707 21.703L15.084 19.327C15.177 19.2341 15.2508 19.1238 15.3011 19.0025C15.3514 18.8811 15.3773 18.7509 15.3773 18.6195C15.3773 18.4881 15.3514 18.358 15.3011 18.2366C15.2508 18.1152 15.177 18.0049 15.084 17.912L12.707 15.536C12.5195 15.3485 12.2652 15.2432 12 15.2432C11.7349 15.2432 11.4806 15.3485 11.293 15.536L8.91603 17.912Z"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M8.91602 4.674C8.72855 4.86153 8.62323 5.11583 8.62323 5.381C8.62323 5.64616 8.72855 5.90047 8.91602 6.088L11.293 8.464C11.4805 8.65147 11.7349 8.75678 12 8.75678C12.2652 8.75678 12.5195 8.65147 12.707 8.464L15.084 6.088C15.2715 5.90047 15.3768 5.64616 15.3768 5.381C15.3768 5.11583 15.2715 4.86153 15.084 4.674L12.707 2.297C12.5195 2.10953 12.2652 2.00421 12 2.00421C11.7349 2.00421 11.4805 2.10953 11.293 2.297L8.91602 4.674Z"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
}

// User Icon
export function UserIcon(props: React.ComponentProps<'svg'>) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      viewBox="0 0 24 24"
      strokeWidth="1.5"
      fill="none"
      stroke="currentColor"
      {...props}
    >
      <path
        d="M16 21V19C16 17.9391 15.5786 16.9217 14.8284 16.1716C14.0783 15.4214 13.0609 15 12 15H6C4.93913 15 3.92172 15.4214 3.17157 16.1716C2.42143 16.9217 2 17.9391 2 19V21"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M16 3.12799C16.8578 3.35036 17.6174 3.85125 18.1597 4.55205C18.702 5.25285 18.9962 6.11388 18.9962 6.99999C18.9962 7.8861 18.702 8.74713 18.1597 9.44793C17.6174 10.1487 16.8578 10.6496 16 10.872"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M22 21V19C21.9993 18.1137 21.7044 17.2528 21.1614 16.5523C20.6184 15.8519 19.8581 15.3516 19 15.13"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M9 11C11.2091 11 13 9.20914 13 7C13 4.79086 11.2091 3 9 3C6.79086 3 5 4.79086 5 7C5 9.20914 6.79086 11 9 11Z"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
}

// Audit Log Icon
export function AuditLogIcon(props: React.ComponentProps<'svg'>) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      viewBox="0 0 24 24"
      stroke-width="1.5"
      fill="none"
      stroke="currentColor"
      {...props}
    >
      <path d="M8 2V6" strokeLinecap="round" strokeLinejoin="round" />
      <path d="M12 2V6" strokeLinecap="round" strokeLinejoin="round" />
      <path d="M16 2V6" strokeLinecap="round" strokeLinejoin="round" />
      <path
        d="M18 4H6C4.89543 4 4 4.89543 4 6V20C4 21.1046 4.89543 22 6 22H18C19.1046 22 20 21.1046 20 20V6C20 4.89543 19.1046 4 18 4Z"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path d="M8 10H14" strokeLinecap="round" strokeLinejoin="round" />
      <path d="M8 14H16" strokeLinecap="round" strokeLinejoin="round" />
      <path d="M8 18H13" strokeLinecap="round" strokeLinejoin="round" />
    </svg>
  );
}

// Insights Icon
export function InsightsIcon(props: React.ComponentProps<'svg'>) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      viewBox="0 0 24 24"
      strokeWidth="1.5"
      fill="none"
      stroke="currentColor"
      {...props}
    >
      <path
        d="M21 12C21.552 12 22.005 11.551 21.95 11.002C21.7195 8.70615 20.7021 6.56068 19.0703 4.9293C17.4386 3.29792 15.2929 2.28102 12.997 2.05098C12.447 1.99598 11.999 2.44898 11.999 3.00098V11.001C11.999 11.2662 12.1044 11.5206 12.2919 11.7081C12.4795 11.8956 12.7338 12.001 12.999 12.001L21 12Z"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M21.21 15.89C20.5738 17.3945 19.5788 18.7202 18.3119 19.7513C17.0449 20.7824 15.5447 21.4874 13.9424 21.8048C12.3401 22.1222 10.6844 22.0422 9.12012 21.5718C7.55585 21.1015 6.1306 20.2551 4.969 19.1067C3.80739 17.9583 2.94479 16.5428 2.45661 14.984C1.96843 13.4252 1.86954 11.7705 2.16857 10.1647C2.46761 8.5588 3.15547 7.05064 4.17202 5.77205C5.18857 4.49345 6.50286 3.48333 7.99998 2.83002"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
}

//Scan Icon
export function ScanIcon(props: React.ComponentProps<'svg'>) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      viewBox="0 0 24 24"
      strokeWidth="1.5"
      fill="none"
      stroke="currentColor"
      {...props}
    >
      <path
        d="M3 7V5C3 4.46957 3.21071 3.96086 3.58579 3.58579C3.96086 3.21071 4.46957 3 5 3H7"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M17 3H19C19.5304 3 20.0391 3.21071 20.4142 3.58579C20.7893 3.96086 21 4.46957 21 5V7"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M21 17V19C21 19.5304 20.7893 20.0391 20.4142 20.4142C20.0391 20.7893 19.5304 21 19 21H17"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M7 21H5C4.46957 21 3.96086 20.7893 3.58579 20.4142C3.21071 20.0391 3 19.5304 3 19V17"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M12 15C13.6569 15 15 13.6569 15 12C15 10.3431 13.6569 9 12 9C10.3431 9 9 10.3431 9 12C9 13.6569 10.3431 15 12 15Z"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path d="M16 16L14.1 14.1" strokeLinecap="round" strokeLinejoin="round" />
    </svg>
  );
}

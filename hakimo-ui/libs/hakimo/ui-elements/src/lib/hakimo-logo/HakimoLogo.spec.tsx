import { render } from '@testing-library/react';

import HakimoLogo from './HakimoLogo';

describe('HakimoLogo', () => {
  it('should render small logo', () => {
    const { container } = render(<HakimoLogo />);

    const img = container.querySelector('img');
    expect(img).toHaveClass('h-9 w-auto');
    expect(img?.getAttribute('src')).toBe('assets/hakimo-dark.svg');
    expect(img?.getAttribute('alt')).toBe('Hakimo logo');
  });

  it('should render full logo', () => {
    const { container } = render(<HakimoLogo full />);

    const img = container.querySelector('img');
    expect(img).toHaveClass('h-9 w-auto');
    expect(img?.getAttribute('src')).toBe('assets/hakimo-full-dark.svg');
    expect(img?.getAttribute('alt')).toBe('Hakimo logo');
  });
});

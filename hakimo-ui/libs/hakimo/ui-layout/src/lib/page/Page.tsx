import {
  AuditLogIcon,
  DesktopFrameIcon,
  DoorGrpIcon,
  DoorsIcon,
  InsightsIcon,
  LocationIcon,
  ScanIcon,
  UserIcon,
} from '@hakimo-ui/hakimo/ui-elements';
import { eventTracker, pageTitleAtom } from '@hakimo-ui/hakimo/util';
import { Button } from '@hakimo-ui/shared/ui-base';
import {
  ArrowLeftIcon,
  ArrowsPointingInIcon,
  BellAlertIcon,
  BoltIcon,
  SpeakerWaveIcon,
  VideoCameraIcon,
} from '@heroicons/react/24/outline';
import clsx from 'clsx';
import { useSetAtom } from 'jotai';
import { ReactNode, useEffect, useMemo } from 'react';

interface Props {
  title: string;
  subtitle?: ReactNode;
  children: ReactNode;
  secondary?: ReactNode;
  fullWidth?: boolean;
  onClickBack?: () => void;
}

const iconMap: Record<string, React.ElementType> = {
  Monitoring: DesktopFrameIcon,
  Alarms: BellAlertIcon,
  'Location Alarms': ArrowsPointingInIcon,
  Doors: DoorsIcon,
  Cameras: VideoCameraIcon,
  'Audio Devices': SpeakerWaveIcon,
  Locations: LocationIcon,
  'Door Groups': DoorGrpIcon,
  Users: UserIcon,
  'Audit Log': AuditLogIcon,
  Insights: InsightsIcon,
  Scan: ScanIcon,
  'Scan Escalations': BoltIcon,
};

export function Page(props: Props) {
  const {
    title,
    subtitle,
    children,
    secondary,
    fullWidth = false,
    onClickBack,
  } = props;

  const setTitleNode = useSetAtom(pageTitleAtom);

  const titleNode = useMemo(() => {
    const Icon = iconMap[title];
    return (
      <div className="flex items-center space-x-2">
        {onClickBack && (
          <Button
            variant="icon"
            onClick={onClickBack}
            onSideEffect={eventTracker('navigate_back_page')}
            className="mr-2"
          >
            <ArrowLeftIcon className="w-6" />
          </Button>
        )}
        {Icon && <Icon className="dark:text-dark-text h-6 w-6 text-gray-800" />}
        <div>
          <h1 className="dark:text-dark-text text-2xl leading-8 text-gray-800">
            {title}
          </h1>
          <h2 className="pt-0.5 text-sm text-gray-600 dark:text-gray-300">
            {subtitle}
          </h2>
        </div>
      </div>
    );
  }, [onClickBack, subtitle, title]);

  useEffect(() => {
    setTitleNode(titleNode);
  }, [setTitleNode, titleNode]);

  return (
    <main className="dark:bg-ondark-bg-1 dark:text-dark-text bg-onlight-bg-1 h-full overflow-y-auto">
      <div
        className={clsx(
          'mx-auto mt-2 h-full px-4 sm:px-6 md:px-2',
          !fullWidth && 'max-w-container'
        )}
      >
        <div className="flex h-full flex-col">
          {secondary && (
            <div className="mb-2 flex justify-end">{secondary}</div>
          )}
          <div className="flex min-h-0 flex-1 flex-col pb-8">{children}</div>
        </div>
      </div>
    </main>
  );
}

export default Page;

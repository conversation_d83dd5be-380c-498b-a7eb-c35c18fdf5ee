import { NavItem, RootHeader, Sidebar } from '@hakimo-ui/hakimo/ui-navigation';
import { draggableNodeAtom } from '@hakimo-ui/hakimo/util';
import { useAtomValue } from 'jotai';
import { ReactElement, useState } from 'react';

interface Props {
  children: ReactElement;
  navItems: NavItem[];
}

export function RootLayout(props: Props) {
  const { children, navItems } = props;
  const draggableAtom = useAtomValue(draggableNodeAtom);
  const [sidebarExpanded, setSidebarExpanded] = useState(false);

  return (
    <div className="dark:bg-dark-bg flex h-screen dark:text-white">
      <div className="ease-in-out">
        <Sidebar
          open={sidebarExpanded}
          navItems={navItems}
          expandSidebar={() => setSidebarExpanded(true)}
          collapseSidebar={() => setSidebarExpanded(false)}
        />
      </div>
      <div
        className="flex min-w-0 flex-1 flex-col transition-all duration-300 ease-in-out"
        data-testid="page-container"
      >
        <RootHeader />
        <div className="min-h-0 flex-1 overflow-y-auto">{children}</div>
        {draggableAtom}
      </div>
    </div>
  );
}

export default RootLayout;

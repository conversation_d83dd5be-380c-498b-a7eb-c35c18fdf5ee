import { AlarmGroup } from '@hakimo-ui/hakimo/types';
import useAuthenticatedRequest from '../shared/useAuthenticatedRequest';

export function useSharedAlarmGroup(sharedToken: string) {
  const url = `http://127.0.0.1:8080/shared/v2/alarm_group?sharedToken=${sharedToken}`;
  const request = new Request(url);

  return useAuthenticatedRequest<AlarmGroup>(request, {
    queryKey: ['alarmGroups', 'shared', sharedToken],
    responseModifier: async (response) => {
      const respJson = await response.json();
      return respJson.payload;
    },
    noAuth: true,
  });
}

export default useSharedAlarmGroup;

import { SharedResourceTokenResponseDTO } from '@hakimo-ui/hakimo/types';
import useAuthenticatedRequest from '../shared/useAuthenticatedRequest';
import dayjs from 'dayjs';

export type ResourceType = 'alarm' | 'location-alarm' | 'live-view' | 'alarm-group';

export function useSharedResourceToken(
  resourceId: string,
  duration: number,
  durationUnit: string,
  resourceType: ResourceType,
  timestamp?: string
) {
  const url = getSharedResourceTokenUrl(
    resourceId,
    resourceType,
    duration,
    durationUnit,
    timestamp
  );
  const request = new Request(url);

  return useAuthenticatedRequest<SharedResourceTokenResponseDTO>(request, {
    responseModifier: async (response) => {
      const respJson = await response.json();
      return respJson.payload;
    },
    enabled: false,
    cacheTime: 0,
  });
}

function getSharedResourceTokenUrl(
  resourceId: string,
  resourceType: ResourceType,
  duration: number,
  durationUnit: string,
  timestamp?: string
) {
  let resourceName = '';
  let path = '';
  switch (resourceType) {
    case 'alarm':
      resourceName = 'alarm';
      break;
    case 'location-alarm':
      resourceName = 'location_alarm';
      break;
    case 'live-view':
      resourceName = 'live_view';
      break;
    case 'alarm-group':
      resourceName = 'alarm_group';
      break;
  }
  let partitionKey;
  if (timestamp) {
    const date = dayjs(timestamp);
    partitionKey = date.format('YYYYMM');
  } else {
    // Use current date if no timestamp provided
    const date = dayjs();
    partitionKey = date.format('YYYYMM');
  }

  path = `/v2/orm/shared/${resourceName}/token/${resourceId}?duration=${duration}&durationUnit=${durationUnit}&partitionKey=${partitionKey}`;
  return path;
}

export default useSharedResourceToken;

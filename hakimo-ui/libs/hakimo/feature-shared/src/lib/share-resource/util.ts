import { ResourceType } from '@hakimo-ui/hakimo/data-access';
import { range } from '@hakimo-ui/hakimo/util';
import dayjs from 'dayjs';

export function getSharedResourcePath(
  resourceType: ResourceType,
  accessToken: string,
  timestamp?: string
) {
  let path = '';
  let resourceName = '';
  switch (resourceType) {
    case 'alarm':
      resourceName = 'alarm';
      break;
    case 'location-alarm':
      resourceName = 'location-alarm';
      break;
    case 'live-view':
      resourceName = 'live-view';
      break;
    case 'alarm-group':
      resourceName = 'alarm-group';
      break;
  }
  let partitionKey;
  if (timestamp) {
    const date = dayjs(timestamp).utc();
    partitionKey = date.format('YYYYMM');
  }
  path = `/shared/${resourceName}?sharedToken=${accessToken}${
    partitionKey ? `&partitionKey=${partitionKey}` : ''
  }`;
  return path;
}

export type DurationUnit = 'hour' | 'day' | 'week';

export interface DurationOption {
  unit: DurationUnit;
  allowedValues: number[];
}

const liveViewOptions: DurationOption[] = [
  {
    unit: 'hour',
    allowedValues: range(1, 12),
  },
];

const allOptions: DurationOption[] = [
  {
    unit: 'hour',
    allowedValues: range(1, 23),
  },
  {
    unit: 'day',
    allowedValues: range(1, 6),
  },
  {
    unit: 'week',
    allowedValues: range(1, 6),
  },
];

export function getDurationOptions(resourceType: ResourceType) {
  if (resourceType === 'live-view') {
    return liveViewOptions;
  } else {
    return allOptions;
  }
}

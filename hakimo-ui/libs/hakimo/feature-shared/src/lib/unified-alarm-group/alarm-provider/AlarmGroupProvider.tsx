/* eslint-disable max-lines */
import {
  Location,
  NeighbourAlarmResponse,
  UnifiedAlarm,
  UnifiedAlarmMessageType,
} from '@hakimo-ui/hakimo/types';
import {
  createContext,
  ReactNode,
  useCallback,
  useContext,
  useEffect,
  useMemo,
  useRef,
  useState,
} from 'react';
import { AlarmPreloaderConfiguration } from '../constants';
import { useAlarmState } from '../hooks/useAlarmState';
import { useUnifiedAlarmWsMessageHandler } from '../hooks/useUnifiedAlarmWsMessageHandler';
import {
  AlarmGroupContextType,
  CameraAlarmTracker,
  TimelineBounds,
} from '../types';
import { getStartAndEndTime } from '../utils';
import { AlarmPreloader } from './AlarmPreloader';
import { getTimelineGaps } from './util';

const defaultTimelineBounds: TimelineBounds = {
  alarmGroupStartTime: 0,
  alarmGroupEndTime: 0,
  totalTimelineStartTime: 0,
  totalTimelineEndTime: 0,
};

const AlarmGroupContext = createContext<AlarmGroupContextType>({
  isBuffering: true,
  seekTo: () => ({}),
  showCameraIds: new Set(),
  camToDetailsMap: {},
  alarmToDetailsMap: {},
  alarmCameraIds: [],
  tenantAdditionalCameraIds: [],
  extraCamDetailsMap: {},
  updateExtraCamFromTenant: () => ({}),
  timelineBounds: defaultTimelineBounds,
  seekerTimestamp: 0,
  alarmGroupId: '',
  isSeekerPaused: false,
  toggleSeekerPaused: () => ({}),
  timelineGaps: [],
  camToAlarmTracker: new Map(),
  updateAlarmLoadState: () => ({}),
  preloadCameraIds: new Set(),
  toggleShowMinorAlarms: () => ({}),
  showMinorAlarms: true,
  allCombinedAlarms: [],
  playbackSpeed: 1,
  updatePlaybackSpeed: () => ({}),
  resetTimeline: () => ({}),
});

export const useAlarmGroupProvider = () => useContext(AlarmGroupContext);

interface Props {
  alarmGroupId: string;
  alarms: UnifiedAlarm[];
  location: Location;
  children: ReactNode;
  isScanAlarmGroup?: boolean;
}

export function AlarmGroupProvider(props: Props) {
  const { alarms, children, location, alarmGroupId, isScanAlarmGroup } = props;
  // Camera Ids which are currently shown in Global Alarm mode
  const [showCameraIds, setShowCameraIds] = useState(new Set<string>());
  const [preloadCameraIds, setPreloadCameraIds] = useState(new Set<string>());
  const [showMinorAlarms, setShowMinorAlarms] = useState<boolean>(true);

  const [isBuffering, setBuffering] = useState(true);
  const preloaderRef = useRef<AlarmPreloader | null>(null);
  const initialAlarmsRef = useRef<UnifiedAlarm[]>(alarms);
  // initial Alarm group start time and end time
  const initialAlarmGroupBoundsRef = useRef<number[]>(
    getStartAndEndTime(alarms)
  );
  const [seekerTimestamp, setSeekerTimestamp] = useState(
    initialAlarmGroupBoundsRef.current[0]
  );
  const [isSeekerPaused, setIsSeekerPaused] = useState(false);
  const [neighbourAlarms, setNeighbourAlarms] = useState<UnifiedAlarm[]>([]);
  const [timelineGaps, setTimelineGaps] = useState<Array<[number, number]>>([]);
  const [camToAlarmTracker, setCamToAlarmTracker] = useState<
    Map<string, CameraAlarmTracker>
  >(new Map());
  const [playbackSpeed, setPlaybackSpeed] = useState(1);

  const toggleSeekerPaused = () => setIsSeekerPaused((prev) => !prev);
  const toggleShowMinorAlarms = (val: boolean) => setShowMinorAlarms(val);
  const updatePlaybackSpeed = (speed: number) => setPlaybackSpeed(speed);

  // filter out resolved alarms if showMinorAlarms is false
  const updatedNeighbourAlarms = useMemo(() => {
    if (!showMinorAlarms) {
      return neighbourAlarms.filter((alarm) => !alarm.resolvedAlarm);
    }
    return neighbourAlarms;
  }, [neighbourAlarms, showMinorAlarms]);

  const updatedAlarms = useMemo(() => {
    if (!showMinorAlarms) {
      return alarms.filter((alarm) => !alarm.resolvedAlarm);
    }
    return alarms;
  }, [alarms, showMinorAlarms]);

  const {
    alarmCameraIds,
    camToDetailsMap,
    tenantAdditionalCameraIds,
    extraCamDetailsMap,
    updateExtraCamFromTenant,
    alarmToDetailsMap,
  } = useAlarmState(updatedAlarms, updatedNeighbourAlarms);

  const [timelineBounds, setTimelineBounds] = useState<TimelineBounds>(
    defaultTimelineBounds
  );
  const increaseSeekerTimeRef = useRef<(diffInMs: number) => void>();

  const handleNeighbourAlarmsResponse = (data: NeighbourAlarmResponse) => {
    setNeighbourAlarms((prev) => [...prev, ...data.payload.alarms]);
    const { startTimestamp, endTimestamp } = data.payload;
    const newTotalTimelineStartTime = Math.min(
      timelineBounds.totalTimelineStartTime,
      startTimestamp
    );
    const newTotalTimelineEndTime = Math.max(
      timelineBounds.totalTimelineEndTime,
      endTimestamp
    );
    setTimelineBounds((prev) => ({
      ...prev,
      totalTimelineStartTime: newTotalTimelineStartTime,
      totalTimelineEndTime: newTotalTimelineEndTime,
    }));
  };

  const resetTimeline = () => {
    setTimelineBounds((prev) => ({
      ...prev,
      totalTimelineStartTime: prev.alarmGroupStartTime,
      totalTimelineEndTime: prev.alarmGroupEndTime,
    }));
    setNeighbourAlarms([]);
  };

  useUnifiedAlarmWsMessageHandler(
    UnifiedAlarmMessageType.FETCH_ALARMS,
    handleNeighbourAlarmsResponse
  );

  useEffect(() => {
    const totalTimelineStartTime = timelineBounds.totalTimelineStartTime;
    const totalTimelineEndTime = timelineBounds.totalTimelineEndTime;
    const newTimelineGaps = getTimelineGaps(
      [...updatedAlarms, ...updatedNeighbourAlarms],
      totalTimelineStartTime,
      totalTimelineEndTime
    );
    setTimelineGaps(newTimelineGaps);
  }, [
    updatedAlarms,
    updatedNeighbourAlarms,
    timelineBounds.totalTimelineEndTime,
    timelineBounds.totalTimelineStartTime,
  ]);

  // seekertimestamp, alarmgroup start & end time, total timeline start & end time,

  useEffect(() => {
    const [alarmGroupStartTime, alarmGroupEndTime] =
      getStartAndEndTime(updatedAlarms);
    const [totalTimelineStartTime, totalTimelineEndTime] = [
      alarmGroupStartTime,
      alarmGroupEndTime,
    ];
    setTimelineBounds((prev) => {
      const newVal = { ...prev, alarmGroupStartTime, alarmGroupEndTime };
      if (
        prev.totalTimelineStartTime === 0 ||
        totalTimelineStartTime < prev.totalTimelineStartTime
      ) {
        newVal['totalTimelineStartTime'] = totalTimelineStartTime;
      }
      if (
        prev.totalTimelineEndTime === 0 ||
        totalTimelineEndTime > prev.totalTimelineEndTime
      ) {
        newVal.totalTimelineEndTime = totalTimelineEndTime;
      }
      return newVal;
    });
  }, [updatedAlarms]);

  useEffect(() => {
    const hasFrameBasedMedia = initialAlarmsRef.current.every(
      (alarm) => alarm.media.type === 'frames'
    );
    // no need of preloader for scan alarm group as media type is frames
    if (isScanAlarmGroup || hasFrameBasedMedia) {
      setBuffering(false);
    }
    preloaderRef.current = new AlarmPreloader(
      initialAlarmsRef.current,
      AlarmPreloaderConfiguration
    );

    const preloader = preloaderRef.current;
    if (!isScanAlarmGroup && !hasFrameBasedMedia) {
      preloader.onBufferReady(() => setBuffering(false));
      preloader.onBufferNotReady(() => setBuffering(true));
    }
    preloader.onCurrentShowCameraIdsChange(() =>
      setShowCameraIds(preloader.getCurrentShowCameraIds())
    );
    preloader.onCamToAlarmTrackerChange(() =>
      setCamToAlarmTracker(preloader.getCamToAlarmTracker())
    );
    preloader.onPreloadCameraIdsChange(() =>
      setPreloadCameraIds(preloader.getCurrentPreloadCameraIds())
    );
    // Initial load at time 0
    preloader.moveTo(initialAlarmGroupBoundsRef.current[0]);
  }, [isScanAlarmGroup]);

  useEffect(() => {
    if (preloaderRef.current) {
      preloaderRef.current.updateAlarms([
        ...updatedAlarms,
        ...updatedNeighbourAlarms,
      ]);
    }
  }, [updatedAlarms, updatedNeighbourAlarms]);

  const updateAlarmLoadState = (alarmId: string, isReady: boolean) => {
    if (preloaderRef.current) {
      preloaderRef.current.updateAlarmLoadState(alarmId, isReady);
    }
  };

  const seekTo = useCallback(
    (t: number) => {
      let newTime = t;
      const relevantTimlineGap = timelineGaps.find(
        (gap) => newTime >= gap[0] && newTime <= gap[1]
      );
      if (relevantTimlineGap) {
        newTime = relevantTimlineGap[1];
      }
      preloaderRef.current?.moveTo(newTime);
      setSeekerTimestamp(newTime);
    },
    [timelineGaps]
  );

  const increaseSeekerTime = useCallback(
    (diffInMs: number) => {
      setSeekerTimestamp((prev) => {
        let newTime = prev + diffInMs;
        if (newTime > timelineBounds.totalTimelineEndTime) {
          return timelineBounds.totalTimelineStartTime;
        }

        const relevantTimlineGap = timelineGaps.find(
          (gap) => newTime >= gap[0] && newTime <= gap[1]
        );
        if (relevantTimlineGap) {
          newTime = relevantTimlineGap[1];
        }
        preloaderRef.current?.moveTo(newTime);
        return newTime;
      });
    },
    [
      timelineGaps,
      timelineBounds.totalTimelineEndTime,
      timelineBounds.totalTimelineStartTime,
    ]
  );

  increaseSeekerTimeRef.current = increaseSeekerTime;

  useEffect(() => {
    if (isBuffering) return; // don’t advance while buffering
    const timer = window.setInterval(() => {
      if (isBuffering || isSeekerPaused) {
        return;
      }
      increaseSeekerTimeRef.current?.(1000);
    }, 1000 / playbackSpeed);
    return () => clearInterval(timer);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isBuffering, isSeekerPaused, playbackSpeed]);

  const allCombinedAlarms = useMemo(() => {
    return [...alarms, ...neighbourAlarms];
  }, [alarms, neighbourAlarms]);

  const contextValue = {
    isBuffering,
    seekTo,
    showCameraIds,
    camToDetailsMap,
    extraCamDetailsMap,
    updateExtraCamFromTenant,
    alarmCameraIds,
    tenantAdditionalCameraIds,
    location,
    timelineBounds,
    seekerTimestamp,
    alarmGroupId,
    alarmToDetailsMap,
    isSeekerPaused,
    toggleSeekerPaused,
    timelineGaps,
    camToAlarmTracker,
    updateAlarmLoadState,
    preloadCameraIds,
    toggleShowMinorAlarms,
    showMinorAlarms,
    allCombinedAlarms,
    playbackSpeed,
    updatePlaybackSpeed,
    resetTimeline,
    isScanAlarmGroup,
  };

  return (
    <AlarmGroupContext.Provider value={contextValue}>
      {children}
    </AlarmGroupContext.Provider>
  );
}

export default AlarmGroupProvider;

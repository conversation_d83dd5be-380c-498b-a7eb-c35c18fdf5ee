/* eslint-disable max-lines */
import {
  StatusType,
  AlarmGroup,
  UnifiedAlarmGroup,
} from '@hakimo-ui/hakimo/types';
import { useFullscreen } from '@hakimo-ui/hakimo/util';
import clsx from 'clsx';
import { useRef, useState } from 'react';
import { useNavigate, unstable_usePrompt as usePrompt } from 'react-router-dom';
import AlarmGroupMedia from './alarm-group-media/AlarmGroupMedia';
import { AlarmGroupProvider } from './alarm-provider/AlarmGroupProvider';
import AlarmGroupToolbar from './controls/AlarmGroupToolbar';
import Floorplan from './Floorplan';
import { AlarmGroupHeader } from './header/AlarmGroupHeader';
import ScanSOPWrapper from './ScanSOPWrapper';
import { SOP as LocationSOP } from '../location-sop/SOP';
import { SOP as AlarmGroupSOP } from './SOP';
import { CamAction, CameraFeedMode, EscalationState } from './types';
import { normalizeAlarmGroupLocation } from './utils';
import { AlarmGroupUpdates } from '../alarm-group-updates/AlarmGroupUpdates';

interface Props {
  alarmGroup: AlarmGroup | UnifiedAlarmGroup;
  isScanAlarmGroup?: boolean;
  isFullPageMode?: boolean;
  showSOP?: boolean;
  escalationState?: EscalationState;
  onResolveEscalation?: (comment?: string) => void;
  onCreateEscalation?: (comment?: string) => void;
  onCloseInvestigateModal?: () => void;
  handleCamAction?: (actionType: CamAction) => (comment?: string) => void;
}

export function AlarmGroupContainer({
  alarmGroup,
  isScanAlarmGroup,
  isFullPageMode,
  showSOP = true,
  escalationState,
  onResolveEscalation,
  onCreateEscalation,
  onCloseInvestigateModal,
  handleCamAction,
}: Props) {
  const navigate = useNavigate();
  const [canExit, setCanExit] = useState<boolean>(false);
  const containerRef = useRef<HTMLDivElement>(null);
  const { isFullScreen, toggleFullScreen } = useFullscreen(containerRef);
  const [globalCamMode, setGlobalCamMode] = useState<CameraFeedMode>(
    CameraFeedMode.ALARM_VIDEO
  );

  const onAlarmResolve = (status: StatusType) => {
    if (status === 'Resolved') {
      setCanExit(true);
      // Wait for a tick to allow the canExit state to be set
      setTimeout(() => navigate('/monitoring'));
    }
  };

  const onChangeGlobalCamMode = (mode: CameraFeedMode) =>
    setGlobalCamMode(mode);

  const adaptedLocation = normalizeAlarmGroupLocation(
    alarmGroup.location,
    alarmGroup.tenantId
  );

  usePrompt({
    when: !canExit && !isScanAlarmGroup,
    message: 'If you leave now, this alarm will be dropped!',
  });

  return (
    <AlarmGroupProvider
      alarmGroupId={alarmGroup.id}
      alarms={alarmGroup.alarms}
      location={adaptedLocation}
      isScanAlarmGroup={isScanAlarmGroup}
    >
      <div
        ref={containerRef}
        className={clsx(
          'divide-onlight-line-3 dark:divide-ondark-line-3 bg-onlight-bg-1 dark:bg-ondark-bg-1 flex gap-4',
          isFullPageMode ? 'min-h-screen' : 'h-full',
          isScanAlarmGroup && 'divide-x',
          isFullScreen && 'p-4'
        )}
      >
        <div
          className={clsx(
            'flex flex-col gap-8',
            isFullPageMode ? 'min-h-full' : 'h-full',
            isScanAlarmGroup ? 'w-3/4' : 'w-full'
          )}
        >
          <div
            className={clsx(
              'flex flex-col divide-y dark:divide-white/10',
              isFullPageMode ? 'min-h-[100vh]' : 'h-full min-h-full'
            )}
          >
            <AlarmGroupHeader
              alarmGroupId={alarmGroup.id}
              alarmStatus={alarmGroup.status}
              location={adaptedLocation}
              globalCamMode={globalCamMode}
              onChangeGlobalCamMode={onChangeGlobalCamMode}
              isFullScreen={isFullScreen}
              toggleFullScreen={toggleFullScreen}
              escalation={
                alarmGroup.escalations && alarmGroup.escalations.length > 0
                  ? alarmGroup.escalations[0]
                  : undefined
              }
              isScanAlarmGroup={isScanAlarmGroup}
              scanEscalationState={escalationState}
              onResolveEscalation={onResolveEscalation}
            />

            <AlarmGroupMedia globalCamMode={globalCamMode} />
            <AlarmGroupToolbar />
          </div>
          {!isScanAlarmGroup && !isFullPageMode && (
            <div className="mt-4 flex gap-4">
              <div className="flex flex-1 flex-col gap-4">
                <Floorplan locationId={String(adaptedLocation.id)} />
                <AlarmGroupUpdates alarmGroupId={alarmGroup.id} />
              </div>
              <div className="flex-1">
                <LocationSOP
                  locationId={String(adaptedLocation.id)}
                  tenantId={adaptedLocation.tenant_id}
                />
              </div>
            </div>
          )}
          {isFullPageMode && (
            <div className="mt-4 flex gap-4">
              <div className="flex flex-1 flex-col gap-4">
                <Floorplan locationId={String(adaptedLocation.id)} />
                <AlarmGroupUpdates alarmGroupId={alarmGroup.id} />
              </div>
              <div className="flex-1">
                <LocationSOP
                  locationId={String(adaptedLocation.id)}
                  tenantId={adaptedLocation.tenant_id}
                />
              </div>
            </div>
          )}
        </div>
        {showSOP && (
          <div className="w-1/4 pl-2">
            {isScanAlarmGroup ? (
              <ScanSOPWrapper
                escalationState={escalationState}
                locationId={String(adaptedLocation.id)}
                tenantId={adaptedLocation.tenant_id ?? ''}
                onResolveEscalation={onResolveEscalation}
                onCreateEscalation={onCreateEscalation}
                onCloseInvestigateModal={onCloseInvestigateModal}
                handleCamAction={handleCamAction}
              />
            ) : (
              <AlarmGroupSOP
                alarmGroup={alarmGroup}
                onAlarmResolve={onAlarmResolve}
              />
            )}
          </div>
        )}
      </div>
    </AlarmGroupProvider>
  );
}

export default AlarmGroupContainer;

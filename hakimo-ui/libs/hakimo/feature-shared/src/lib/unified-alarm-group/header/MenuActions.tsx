import {
  FullScreenExitIcon,
  FullScreenIcon,
  MenuDropdown,
} from '@hakimo-ui/hakimo/ui-elements';
import { EllipsisVerticalIcon, LinkIcon, ShareIcon } from '@heroicons/react/24/outline';

interface Props {
  isFullScreen?: boolean;
  toggleFullScreen: () => void;
  onCopyLink: () => void;
  onShare: () => void;
}

export function MenuActions(props: Props) {
  const { isFullScreen, toggleFullScreen, onCopyLink, onShare } = props;

  const alarmMenuOptions = [
    {
      dataTestId: 'btn-share-alarm-group',
      name: 'Share',
      onClick: onShare,
      icon: <ShareIcon className="h-4 w-4" />,
    },
    {
      dataTestId: 'btn-copy-alarm-group-link',
      name: 'Copy link',
      onClick: onCopyLink,
      icon: <LinkIcon className="h-4 w-4" />,
    },
    {
      dataTestId: 'btn-toggle-fullscreen-view',
      name: `${isFullScreen ? 'Exit ' : ''} Fullscreen`,
      onClick: toggleFullScreen,
      group: 'viewMode',
      icon: (
        <span>
          {isFullScreen ? (
            <FullScreenExitIcon className="dark:fill-dark-text h-5 w-5" />
          ) : (
            <FullScreenIcon className="dark:fill-dark-text h-5 w-5" />
          )}
        </span>
      ),
    },
  ];

  const menuButton = (
    <EllipsisVerticalIcon className="h-5 w-5" data-testid="menu-button" />
  );

  return (
    <span className="mr-1">
      <MenuDropdown items={alarmMenuOptions} menuButton={menuButton} />
    </span>
  );
}

export default MenuActions;

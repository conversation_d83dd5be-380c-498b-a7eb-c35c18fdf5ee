import { Escalation, Location } from '@hakimo-ui/hakimo/types';
import { toast } from '@hakimo-ui/hakimo/util';
import { Label, LiveClock } from '@hakimo-ui/shared/ui-base';
import * as Sentry from '@sentry/react';
import { useState } from 'react';
import MonitoringWindowInfo from '../../monitoring-window-info/MonitoringWindowInfo';
import { ShareResource } from '../../share-resource/ShareResource';
import { CameraFeedMode, EscalationState } from '../types';
import { mapStatusToStatusType, mapStatusTypeToLabelType } from '../utils';
import AlarmEscalationBanner from './AlarmEscalationBanner';
import HeaderActions from './HeaderActions';
import MenuActions from './MenuActions';
import ScanEscalationBanner from './ScanEscalationBanner';

interface Props {
  location: Location;
  alarmGroupId: string;
  alarmStatus: string;
  globalCamMode: CameraFeedMode;
  onChangeGlobalCamMode: (mode: CameraFeedMode) => void;
  isFullScreen: boolean;
  toggleFullScreen: () => void;
  escalation?: Escalation;
  scanEscalationState?: EscalationState;
  onResolveEscalation?: (comment?: string) => void;
  isScanAlarmGroup?: boolean;
}

export function AlarmGroupHeader(props: Props) {
  const {
    location,
    alarmGroupId,
    alarmStatus,
    globalCamMode,
    onChangeGlobalCamMode,
    isFullScreen,
    toggleFullScreen,
    escalation,
    isScanAlarmGroup,
    scanEscalationState,
    onResolveEscalation,
  } = props;

  const [openShareAlarmGroup, setOpenShareAlarmGroup] = useState(false);

  const onCopyLink = async () => {
    const alarmGroupLink = `${window.location.origin}/alarm-groups/${alarmGroupId}`;
    try {
      await navigator.clipboard.writeText(alarmGroupLink);
      toast('Alarm group link copied to clipboard');
    } catch (err) {
      Sentry.captureMessage('Error copying text to clipboard');
    }
  };

  const onShare = () => {
    setOpenShareAlarmGroup(true);
  };

  return (
    <div>
      {escalation && (
        <AlarmEscalationBanner alarmId={alarmGroupId} escalation={escalation} />
      )}
      {isScanAlarmGroup && scanEscalationState && onResolveEscalation && (
        <ScanEscalationBanner
          escalationState={scanEscalationState}
          onResolveEscalation={onResolveEscalation}
        />
      )}

      <div
        id="alarm-header"
        className="top-0 flex items-center gap-3 px-4 py-2"
      >
        <div className="flex flex-1 items-center gap-2 overflow-hidden">
          <div className="flex max-w-[60%] flex-shrink-0 flex-col ">
            <span
              className="overflow-hidden text-ellipsis whitespace-nowrap"
              title={`#${alarmGroupId} - ${location?.name}`}
            >
              {location.name}
            </span>
            <div
              title={location.description}
              className="dark:text-ondark-text-2 text-onlight-text-2 overflow-hidden text-ellipsis whitespace-nowrap text-xs"
            >
              {location.description}
            </div>
          </div>
          <div className="h-6 border-l border-black/20 dark:border-white/20"></div>
          <div className="flex-1">
            <Label
              type={mapStatusTypeToLabelType(
                mapStatusToStatusType(alarmStatus)
              )}
            >
              {mapStatusToStatusType(alarmStatus)}
            </Label>
          </div>
        </div>

        <div className="flex-1">
          <span>
            {location.timezone && <LiveClock timezone={location.timezone} />}
          </span>
          <span className="font-mono text-xs">
            <MonitoringWindowInfo locationId={location.id} />
          </span>
        </div>
        <div className="flex items-center gap-2">
          <HeaderActions
            mode={globalCamMode}
            onChangeMode={onChangeGlobalCamMode}
            locationName={location.name}
          />
          <MenuActions
            isFullScreen={isFullScreen}
            onCopyLink={onCopyLink}
            onShare={onShare}
            toggleFullScreen={toggleFullScreen}
          />
        </div>
      </div>

      {openShareAlarmGroup && (
        <ShareResource
          resourceId={alarmGroupId}
          resourceType="alarm-group"
          onClose={() => setOpenShareAlarmGroup(false)}
        />
      )}
    </div>
  );
}

export default AlarmGroupHeader;

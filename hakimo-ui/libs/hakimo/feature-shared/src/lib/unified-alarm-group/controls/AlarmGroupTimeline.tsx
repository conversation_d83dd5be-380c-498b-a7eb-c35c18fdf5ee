/* eslint-disable max-lines */
import { DndContext, DragEndEvent } from '@dnd-kit/core';
import {
  restrictToHorizontalAxis,
  restrictToParentElement,
} from '@dnd-kit/modifiers';
import { useEffect, useMemo, useRef, useState } from 'react';
import { useAlarmGroupProvider } from '../alarm-provider/AlarmGroupProvider';
import CameraTimeline from './CameraTimeline';
import CompressedTimeline from './CompressedTimeline';
import TimeIndicators from './TimeIndicators';
import { TimelineSeeker } from './TimelineSeeker';
import { getTruncatedText } from './utils';

interface Props {
  showNeighbourAlarmActions: boolean;
  isCompressedTimeline: boolean;
}

export function AlarmGroupTimeline(props: Props) {
  const { isCompressedTimeline } = props;
  const {
    alarmCameraIds,
    camToDetailsMap,
    timelineBounds: {
      totalTimelineStartTime,
      totalTimelineEndTime,
      alarmGroupStartTime,
      alarmGroupEndTime,
    },
    location,
    seekTo,
    seekerTimestamp,
  } = useAlarmGroupProvider();
  const timelineLabelRef = useRef<HTMLDivElement>(null);
  const timelineParentRef = useRef<HTMLDivElement>(null);
  const [localSeekerPosition, setLocalSeekerPosition] = useState<number | null>(
    null
  );

  // const sensors = useSensors(
  //   useSensor(PointerSensor, { activationConstraint: { distance: 5 } })
  // );

  // Calculate seeker position in pixels from timestamp
  const calculatedPixelPosition = useMemo(() => {
    const timelineWidth = timelineParentRef.current?.clientWidth || 0;
    const timelineDuration = totalTimelineEndTime - totalTimelineStartTime;
    const diff = seekerTimestamp - totalTimelineStartTime;
    const percentage = (diff / timelineDuration) * 100;
    return (percentage / 100) * timelineWidth;
  }, [seekerTimestamp, totalTimelineEndTime, totalTimelineStartTime]);

  // Convert pixel position to percentage for TimelineSeeker component
  const displayLeftOffset = useMemo(() => {
    const timelineWidth = timelineParentRef.current?.clientWidth || 0;
    if (timelineWidth === 0) return 0;
    const pixelPos = localSeekerPosition ?? calculatedPixelPosition;
    return (pixelPos / timelineWidth) * 100;
  }, [localSeekerPosition, calculatedPixelPosition]);

  // Sync local position with calculated position
  useEffect(() => {
    setLocalSeekerPosition(calculatedPixelPosition);
  }, [calculatedPixelPosition]);

  const alarmGroupTimeline = useMemo(() => {
    return [alarmGroupStartTime, alarmGroupEndTime];
  }, [alarmGroupEndTime, alarmGroupStartTime]);

  const handleDragEnd = (event: DragEndEvent) => {
    const { delta } = event;
    const timelineWidth = timelineParentRef.current?.clientWidth || 0;

    if (timelineWidth === 0) return;

    // Calculate new position in pixels, then clamp to bounds
    const currentPixelPosition = localSeekerPosition ?? calculatedPixelPosition;
    const newPixelPosition = Math.max(
      0,
      Math.min(timelineWidth, currentPixelPosition + delta.x)
    );

    // Update local position immediately
    setLocalSeekerPosition(newPixelPosition);

    // Calculate timestamp and seek
    const percentage = (newPixelPosition / timelineWidth) * 100;
    const timelineDuration = totalTimelineEndTime - totalTimelineStartTime;
    const timeOffset = (percentage / 100) * timelineDuration;
    const newTimestamp = totalTimelineStartTime + timeOffset;

    seekTo(newTimestamp);
  };

  const onClickCamera =
    (camId: string) => (event: React.MouseEvent<HTMLSpanElement>) => {
      if (event.metaKey || event.ctrlKey) {
        const url = `/cameras/?camId=${camId}`;
        window.open(url, '_blank', 'noopener,noreferrer');
      }
    };

  return (
    <div id="toolbar" className="relative flex items-center gap-2 pt-8">
      <div className="flex w-1/6 flex-col gap-1" ref={timelineLabelRef}>
        {!isCompressedTimeline &&
          alarmCameraIds.map((camId) => (
            <span
              title={camToDetailsMap[camId].cameraName}
              key={camId}
              className="h-[14px] cursor-pointer overflow-hidden overflow-ellipsis whitespace-nowrap text-xs"
              tabIndex={1}
              onClick={onClickCamera(camId)}
            >
              {getTruncatedText(
                camToDetailsMap[camId].cameraName,
                timelineLabelRef
              )}
            </span>
          ))}
        {isCompressedTimeline && (
          <span className="h-4 cursor-pointer overflow-hidden overflow-ellipsis whitespace-nowrap text-xs">
            {alarmCameraIds.length} cameras
          </span>
        )}
      </div>
      <DndContext
        onDragEnd={handleDragEnd}
        modifiers={[restrictToParentElement, restrictToHorizontalAxis]}
      >
        <div
          id="timeline-parent"
          className="relative mr-1 flex flex-grow flex-col gap-1"
          ref={timelineParentRef}
        >
          <TimelineSeeker leftOffset={displayLeftOffset} />
          <TimeIndicators
            startTime={totalTimelineStartTime}
            endTime={totalTimelineEndTime}
            timezone={location?.timezone ?? 'UTC'}
            highlightTimes={alarmGroupTimeline}
          />
          {isCompressedTimeline && <CompressedTimeline theme="system" />}
          {!isCompressedTimeline &&
            alarmCameraIds.map((camId) => (
              <div key={camId} className="relative h-[14px]">
                <CameraTimelineWrapper camId={camId} />
              </div>
            ))}
        </div>
      </DndContext>
    </div>
  );
}

export default AlarmGroupTimeline;

interface CameraTimelineWrapperProps {
  camId: string;
}

export function CameraTimelineWrapper(props: CameraTimelineWrapperProps) {
  const { camId } = props;
  const { seekTo, camToAlarmTracker } = useAlarmGroupProvider();
  const currentAlarmId = useMemo(() => {
    const cameraAlarmTracker = camToAlarmTracker.get(camId);
    return cameraAlarmTracker?.currentAlarmId ?? '';
  }, [camId, camToAlarmTracker]);

  const onTimelineClick = (time: number) => seekTo(time);

  return (
    <CameraTimeline
      theme="system"
      cameraId={camId}
      currentAlarmId={currentAlarmId}
      onTimelineClick={onTimelineClick}
    />
  );
}

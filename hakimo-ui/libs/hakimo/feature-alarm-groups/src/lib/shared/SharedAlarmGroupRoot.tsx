import { useSharedAlarmGroup } from '@hakimo-ui/hakimo/data-access';
import { AlarmGroupContainer } from '@hakimo-ui/hakimo/feature-shared';
import { NotFound, QueryResult } from '@hakimo-ui/hakimo/ui-elements';
import { useSearchParams } from 'react-router-dom';

interface Props {
  sharedToken: string;
}

function SharedAlarmGroupDetails(props: Props) {
  const { sharedToken } = props;

  const queryResult = useSharedAlarmGroup(sharedToken);

  return (
    <QueryResult queryResult={queryResult}>
      {(data) => (
        <div className="container mx-auto">
          <AlarmGroupContainer
            alarmGroup={data}
            isFullPageMode={true}
            showSOP={false}
          />
        </div>
      )}
    </QueryResult>
  );
}

export function SharedAlarmGroupRoot() {
  const [searchParams] = useSearchParams();
  const sharedToken = searchParams.get('sharedToken');

  if (sharedToken === null) {
    return (
      <NotFound message="Missing shared token in shared alarm group URL" />
    );
  }

  return <SharedAlarmGroupDetails sharedToken={sharedToken} />;
}

export default SharedAlarmGroupRoot;

import { HakimoLogo } from '@hakimo-ui/hakimo/ui-elements';
import clsx from 'clsx';
import { Link } from 'react-router-dom';

interface Props {
  short?: boolean;
}

function HakimoNavLink(props: Props) {
  const { short } = props;
  return (
    <Link
      to="/"
      className="focus-visible:ring-primary-500 dark:focus:ring-offset-dark-surface block rounded focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-offset-2"
    >
      <div
        className={clsx('h-10', {
          'flex justify-start pl-4': !short,
        })}
      >
        <HakimoLogo full={!short} />
      </div>
    </Link>
  );
}

export default HakimoNavLink;

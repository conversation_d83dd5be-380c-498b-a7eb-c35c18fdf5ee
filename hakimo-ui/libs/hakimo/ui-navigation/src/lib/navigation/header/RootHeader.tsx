import { SystemNotificationPreference } from '@hakimo-ui/hakimo/types';
import {
  isWebNotificationSupported,
  pageTitleAtom,
  systemNotifyAtom,
} from '@hakimo-ui/hakimo/util';
import { useAtomValue, useSetAtom } from 'jotai';
import { useEffect } from 'react';

export function RootHeader() {
  const pageTitleNode = useAtomValue(pageTitleAtom);
  const setSystemNotifyPreference = useSetAtom(systemNotifyAtom);
  useEffect(() => {
    // handle scenario where user has manually denied notification permission
    if (isWebNotificationSupported()) {
      global.Notification.permission !== 'granted' &&
        setSystemNotifyPreference(SystemNotificationPreference.OFF);
    }
  }, [setSystemNotifyPreference]);

  return (
    <div className="dark:bg-ondark-bg-1 dark:text-dark-text bg-onlight-bg-1 sticky top-0 z-10 flex h-12 items-center px-4 py-0.5">
      <div className="flex items-center text-sm font-medium">
        <span>{pageTitleNode}</span>
      </div>
    </div>
  );
}

export default RootHeader;

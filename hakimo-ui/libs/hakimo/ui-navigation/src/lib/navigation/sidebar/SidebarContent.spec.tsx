import { render } from '@testing-library/react';
import SidebarContent from './SidebarContent';

jest.mock('../hakimo/HakimoNavLink', () => {
  return function () {
    return <nav>Hakimo Nav</nav>;
  };
});

jest.mock('./Links', () => {
  return function () {
    return <nav>Nav Links</nav>;
  };
});

jest.mock('@hakimo-ui/hakimo/util', () => ({
  useUser: () => ({ name: 'Test User' }),
  useAuthUtils: () => ({
    logout: jest.fn(),
    profilePic: 'profile.jpg',
  }),
  hasPermission: () => true,
}));

jest.mock('react-router-dom', () => {
  const actual = jest.requireActual('react-router-dom');
  return {
    ...actual,
    useNavigate: () => jest.fn(),
  };
});

describe('SidebarContent', () => {
  it('should render containers with correct class names on expanded sidebar', () => {
    const { container } = render(
      <SidebarContent navItems={[]} toggleSidebar={jest.fn()} />
    );

    const componentRoot = container.firstElementChild;
    expect(componentRoot).toHaveClass('px-2');

    const navLinksContainer = componentRoot?.querySelector('div');
    expect(navLinksContainer).toBeInTheDocument();
  });

  it('should render containers with correct class names on collapsed sidebar', () => {
    const { container } = render(
      <SidebarContent navItems={[]} collapsed toggleSidebar={jest.fn()} />
    );

    const componentRoot = container.firstElementChild;
    expect(componentRoot).toHaveClass('px-2 text-center');
  });
});

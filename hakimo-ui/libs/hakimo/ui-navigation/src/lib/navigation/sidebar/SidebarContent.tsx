import { reset } from '@amplitude/analytics-browser';
import { hasPermission, useAuthUtils, useUser } from '@hakimo-ui/hakimo/util';
import { Menu, Transition } from '@headlessui/react';
import {
  ChevronDoubleLeftIcon,
  ChevronDoubleRightIcon,
} from '@heroicons/react/24/outline';
import { ChevronRightIcon } from '@heroicons/react/24/solid';
import clsx from 'clsx';
import { Fragment } from 'react';
import { useNavigate } from 'react-router-dom';
import HakimoNavLink from '../hakimo/HakimoNavLink';
import Links from './Links';
import { NavItem } from './types';
interface Props {
  collapsed?: boolean;
  navItems: NavItem[];
  toggleSidebar: () => void;
  onClick?: () => void;
}
function SidebarContent(props: Props) {
  const { collapsed, navItems, onClick = () => null, toggleSidebar } = props;
  const user = useUser();
  const { logout, profilePic } = useAuthUtils();
  const navigate = useNavigate();

  const items = navItems.filter((item) =>
    item.requiredPermissions.every((perm) => hasPermission(user, perm))
  );
  const onLogout = () => {
    reset();
    logout({ returnTo: window.location.origin });
  };

  const onClickViewProfile = () => {
    navigate('/profile/general');
  };
  const userMenuItems = [
    {
      name: 'Profile and Settings',
      onClick: onClickViewProfile,
    },
    {
      name: 'Logout',
      onClick: onLogout,
    },
  ];

  return (
    <div
      className={clsx('flex h-full flex-col px-2', collapsed && 'text-center')}
    >
      <div className="flex flex-1 flex-col gap-4 whitespace-nowrap py-1.5">
        {collapsed && <HakimoNavLink short />}
        {!collapsed && <HakimoNavLink />}
        <Links items={items} short={collapsed} onClick={onClick} />
      </div>
      <div className="relative mt-auto flex flex-col items-center pb-4">
        <div className={clsx(collapsed ? 'mb-6' : 'mb-6 ml-2 self-start')}>
          <Menu as="div" className="relative inline-block text-left">
            <Menu.Button
              className="flex items-center space-x-2 pr-2"
              title="User Actions"
            >
              <img
                className={clsx(
                  'h-8 w-8 rounded-full',
                  collapsed &&
                    'border-onlight-line-2 dark:border-ondark-line-2 border'
                )}
                src={profilePic}
                alt="user avatar"
              />
              {!collapsed && (
                <>
                  <span className="truncate text-sm">{user.name}</span>
                  <ChevronRightIcon className="h-4 w-4 text-gray-500 dark:text-gray-400" />
                </>
              )}
            </Menu.Button>
            <Transition
              as={Fragment}
              enter="transition ease-out duration-100"
              enterFrom="transform opacity-0 scale-95"
              enterTo="transform opacity-100 scale-100"
              leave="transition ease-in duration-75"
              leaveFrom="transform opacity-100 scale-100"
              leaveTo="transform opacity-0 scale-95"
            >
              <Menu.Items className="absolute bottom-0 left-full z-50 w-48 origin-bottom-left rounded-md bg-white shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none dark:bg-gray-700">
                {userMenuItems.map((item) => (
                  <Menu.Item key={item.name}>
                    {({ active }) => (
                      <button
                        onClick={item.onClick}
                        className={clsx(
                          'w-full px-4 py-2 text-left text-sm',
                          'text-gray-700 dark:text-white',
                          active
                            ? 'bg-gray-100 text-gray-900 dark:bg-gray-600 dark:text-white'
                            : ''
                        )}
                      >
                        {item.name}
                      </button>
                    )}
                  </Menu.Item>
                ))}
              </Menu.Items>
            </Transition>
          </Menu>
        </div>
        <div
          className={clsx(
            'absolute',
            collapsed
              ? 'bottom-2 left-1/2 -translate-x-1/2'
              : 'bottom-2 right-2'
          )}
        >
          <button
            onClick={toggleSidebar}
            className="text-gray-400 transition hover:text-black dark:hover:text-white"
          >
            {collapsed ? (
              <ChevronDoubleRightIcon className="h-5 w-5" />
            ) : (
              <ChevronDoubleLeftIcon className="h-5 w-5" />
            )}
          </button>
        </div>
      </div>
    </div>
  );
}
export default SidebarContent;

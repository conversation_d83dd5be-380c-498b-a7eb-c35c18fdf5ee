import clsx from 'clsx';
import SidebarContent from './SidebarContent';
import { NavItem } from './types';

interface Props {
  open: boolean;
  navItems: NavItem[];
  expandSidebar: () => void;
  collapseSidebar: () => void;
}

export function Sidebar(props: Props) {
  const { open, navItems, expandSidebar, collapseSidebar } = props;

  return (
    <div
      className={clsx(
        'dark:bg-ondark-bg-1 bg-onlight-bg-1 border-onlight-line-2 dark:border-ondark-line-1',
        'relative z-10 flex h-full flex-col border-r transition-[width] duration-300 ease-in-out',
        open ? 'w-56' : 'w-16'
      )}
    >
      <SidebarContent
        navItems={navItems}
        toggleSidebar={open ? collapseSidebar : expandSidebar}
        collapsed={!open}
      />
    </div>
  );
}

export default Sidebar;

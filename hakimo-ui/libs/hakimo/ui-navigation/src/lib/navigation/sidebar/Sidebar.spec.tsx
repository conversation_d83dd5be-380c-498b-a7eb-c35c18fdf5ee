import { render, screen } from '@testing-library/react';

import Sidebar from './Sidebar';

jest.mock('./SidebarContent', () => {
  return function () {
    return <div>Sidebar Content</div>;
  };
});

beforeAll(() => {
  window.IntersectionObserver = jest.fn(() => {
    return {
      observe: jest.fn(),
      disconnect: jest.fn(),
    } as never;
  });
});

describe('Sidebar', () => {
  it('should render collapsed state', () => {
    const { container } = render(
      <Sidebar
        open={false}
        navItems={[]}
        expandSidebar={jest.fn()}
        collapseSidebar={jest.fn()}
      />
    );

    expect(container).toBeTruthy();
    expect(container.childElementCount).toBe(1);
  });

  it('should render default opened state', () => {
    const { container } = render(
      <Sidebar
        open={true}
        navItems={[]}
        expandSidebar={jest.fn()}
        collapseSidebar={jest.fn()}
      />
    );

    expect(container).toBeTruthy();
    expect(screen.getAllByText(/Sidebar Content/i)).toHaveLength(1);
  });
});

import {
  AuditLogIcon,
  DesktopFrameIcon,
  DoorGrpIcon,
  DoorsIcon,
  InsightsIcon,
  LocationIcon,
  ScanIcon,
  UserIcon,
} from '@hakimo-ui/hakimo/ui-elements';
import { NavItem } from '@hakimo-ui/hakimo/ui-navigation';
import {
  ArrowsPointingInIcon,
  BellAlertIcon,
  BoltIcon,
  SpeakerWaveIcon,
  VideoCameraIcon,
} from '@heroicons/react/24/outline';

export const navItems: NavItem[] = [
  {
    id: 'monitoring',
    name: 'Monitoring',
    href: '/monitoring',
    icon: DesktopFrameIcon,
    requiredPermissions: [
      'location_alarms/monitoring:count',
      'location_alarms/monitoring:get_pending',
    ],
  },
  {
    id: 'alarms',
    name: 'Alarms',
    href: '/alarms',
    icon: BellAlertIcon,
    requiredPermissions: ['alarm:view'],
  },
  {
    id: 'location-alarms',
    name: 'Location Alarms',
    href: '/location-alarms',
    icon: ArrowsPointingInIcon,
    requiredPermissions: ['location_alarm:view'],
  },
  {
    id: 'doors',
    name: 'Doors',
    href: '/doors',
    icon: DoorsIcon,
    requiredPermissions: ['door:view'],
  },
  {
    id: 'cameras',
    name: 'Cameras',
    href: '/cameras',
    icon: VideoCameraIcon,
    requiredPermissions: ['camera:view'],
  },
  {
    id: 'audiodevices',
    name: 'Audio Devices',
    href: '/audiodevices',
    icon: SpeakerWaveIcon,
    requiredPermissions: ['audio_devices:view'],
  },
  {
    id: 'locations',
    name: 'Locations',
    href: '/locations',
    icon: LocationIcon,
    requiredPermissions: ['location:view'],
  },
  {
    id: 'door_groups',
    name: 'Door Groups',
    href: '/doorgroups',
    icon: DoorGrpIcon,
    requiredPermissions: ['door_group:view'],
  },
  {
    id: 'users',
    name: 'Users',
    href: '/users',
    icon: UserIcon,
    requiredPermissions: ['user:view'],
  },
  {
    id: 'auditLog',
    name: 'Audit Log',
    href: '/audit-log',
    icon: AuditLogIcon,
    requiredPermissions: ['audit-log:view'],
  },
  {
    id: 'insights',
    name: 'Insights',
    href: '/insights',
    icon: InsightsIcon,
    requiredPermissions: ['insights:view'],
  },
  {
    id: 'alarm-groups',
    name: 'Alarm Groups',
    href: '/alarm-groups',
    icon: BellAlertIcon,
    requiredPermissions: ['alarm_group:view'],
  },
  {
    id: 'scan',
    name: 'Scan',
    href: '/scan',
    icon: ScanIcon,
    requiredPermissions: ['scan:view'],
  },
  {
    id: 'scan',
    name: 'Scan Escalations',
    href: '/scan-escalations',
    icon: BoltIcon,
    requiredPermissions: ['scan-escalation:view'],
  },
];
